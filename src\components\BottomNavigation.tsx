import { Link, useLocation } from 'react-router-dom';
import { Home, Heart, PlusCircle, User } from 'lucide-react';

export function BottomNavigation() {
  const location = useLocation();
  
  const navItems = [
    {
      path: '/',
      icon: Home,
      label: 'Directory',
      isActive: location.pathname === '/'
    },
    {
      path: '/favorites',
      icon: Heart,
      label: 'Favorites',
      isActive: location.pathname === '/favorites'
    },
    {
      path: '/submit',
      icon: PlusCircle,
      label: 'Submit',
      isActive: location.pathname === '/submit'
    }
  ];

  return (
    <nav className="sm:hidden fixed bottom-0 left-0 right-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur border-t dark:border-gray-700 z-50 safe-area-pb">
      <div className="flex justify-around items-center px-2 py-2 max-w-md mx-auto">
        {navItems.map(({ path, icon: Icon, label, isActive }) => (
          <Link
            key={path}
            to={path}
            className={`flex flex-col items-center gap-1 px-3 py-2 rounded-lg transition-colors min-w-0 flex-1 ${
              isActive
                ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800'
            }`}
          >
            <Icon size={20} className="flex-shrink-0" />
            <span className="text-xs font-medium truncate w-full text-center">{label}</span>
          </Link>
        ))}
      </div>
    </nav>
  );
}
