import { Business } from '../types';
import { isBusinessOpen } from '../lib/businessUtils';
import { useFavorites } from '../hooks/useFavorites';
import FavoriteButton from './FavoriteButton';
import { Star, MapPin, Clock, Phone, Globe, Share2, AlertTriangle, Navigation } from 'lucide-react';
import { getCategoryIcon } from '../lib/categoryIcons';
import { useState } from 'react';
import { ReportIssueModal } from './ReportIssueModal';

interface BusinessCardProps {
  b: Business;
  onClick: () => void;
}

export function BusinessCard({ b, onClick }: BusinessCardProps) {
  const { isFavorite, toggleFavorite } = useFavorites();
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  
  const onFav = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleFavorite(b.name);
  };
  
  const share = async (e: React.MouseEvent) => {
    e.stopPropagation();
    const text = `Check out ${b.name} in ${b.city}!`;
    const url = b.website || window.location.href;
    try {
      if (navigator.share) { 
        await navigator.share({ title: b.name, text, url }); 
      } else { 
        await navigator.clipboard.writeText(url); 
        alert('Link copied to clipboard!'); 
      }
    } catch {}
  };

  const handleReportIssue = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsReportModalOpen(true);
  };

  const handleWebsiteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star 
        key={i} 
        size={16}
        className={`${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300 dark:text-gray-600'}`}
      />
    ));
  };

  const businessStatus = isBusinessOpen(b);
  const CategoryIcon = getCategoryIcon(b.category);

  return (
    <>
      <div 
        className="rounded-xl border border-gray-200 dark:border-gray-700 p-4 shadow-sm bg-white dark:bg-gray-800 cursor-pointer hover:shadow-md dark:hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1"
        onClick={onClick}
      >
        <div className="flex justify-between items-start gap-2">
          <div className="flex-1">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                {/* Category Icon */}
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <CategoryIcon size={16} className="text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{b.name}</h3>
                  <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                    <span>{b.category}</span>
                    <span>•</span>
                    <span>{b.city}</span>
                    {/* Distance Display */}
                    {b.distance && (
                      <>
                        <span>•</span>
                        <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
                          <Navigation size={12} />
                          <span>{b.distance.toFixed(1)} mi</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Business Status Indicator */}
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                businessStatus.isOpen 
                  ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' 
                  : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
              }`}>
                {businessStatus.status}
              </div>
            </div>
            
            {/* Status Details */}
            {businessStatus.nextChange && (
              <p className="text-xs text-gray-600 dark:text-gray-300 mt-1 ml-10">{businessStatus.nextChange}</p>
            )}
            
            {/* Rating Display */}
            {b.rating && (
              <div className="flex items-center gap-1 mt-1 ml-10">
                <div className="flex text-sm">
                  {renderStars(Math.floor(b.rating))}
                </div>
                <span className="text-xs text-gray-600 dark:text-gray-300">
                  {b.rating.toFixed(1)} ({b.reviewCount || 0})
                </span>
              </div>
            )}

            {/* Price Level */}
            {b.price && (
              <div className="mt-1 ml-10">
                <span className="text-green-600 dark:text-green-400 font-medium text-sm">{b.price}</span>
              </div>
            )}

            {/* Top Features */}
            {b.features && b.features.length > 0 && (
              <div className="mt-2 ml-10">
                <div className="flex flex-wrap gap-1">
                  {b.features.slice(0, 2).map((feature, index) => (
                    <span 
                      key={index}
                      className="px-2 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs rounded-full"
                    >
                      {feature}
                    </span>
                  ))}
                  {b.features.length > 2 && (
                    <span className="text-xs text-gray-500 dark:text-gray-400 self-center">
                      +{b.features.length - 2} more
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <FavoriteButton 
              isFavorited={isFavorite(b.name)}
              onToggle={() => onFav({ stopPropagation: () => {} } as React.MouseEvent)}
              size="sm"
            />
            <button 
              onClick={share} 
              className="w-6 h-6 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              aria-label="Share business"
              title="Share business"
            >
              <Share2 size={14} className="text-gray-600 dark:text-gray-300" />
            </button>
            <button 
              onClick={handleReportIssue} 
              className="w-6 h-6 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors"
              aria-label="Report an issue"
              title="Report incorrect information"
            >
              <AlertTriangle size={14} className="text-yellow-600 dark:text-yellow-400" />
            </button>
          </div>
        </div>
        
        {b.description && (
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-300 line-clamp-2 ml-10">{b.description}</p>
        )}
        
        <div className="mt-3 flex gap-3 items-center ml-10">
          {b.website && (
            <a 
              href={b.website} 
              target="_blank" 
              className="text-blue-600 dark:text-blue-400 hover:underline text-sm inline-flex items-center gap-1" 
              rel="noreferrer noopener"
              onClick={handleWebsiteClick}
            >
              <Globe size={14} />
              Website
            </a>
          )}
          {b.phone && (
            <a 
              href={`tel:${b.phone}`} 
              className="text-blue-600 dark:text-blue-400 hover:underline text-sm inline-flex items-center gap-1"
              onClick={(e) => e.stopPropagation()}
            >
              <Phone size={14} />
              Call
            </a>
          )}
          <span className="text-blue-600 dark:text-blue-400 text-sm ml-auto font-medium">View Details →</span>
        </div>
      </div>

      {/* Report Issue Modal */}
      {b.id && (
        <ReportIssueModal
          isOpen={isReportModalOpen}
          onClose={() => setIsReportModalOpen(false)}
          businessId={b.id}
          businessName={b.name}
        />
      )}
    </>
  );
}

