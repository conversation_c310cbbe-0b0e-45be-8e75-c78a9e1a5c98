type Props = {
  categories: string[];
  cities: string[];
  selectedCategory: string;
  selectedCity: string;
  onCategory: (c: string) => void;
  onCity: (c: string) => void;
};
export function Filters({ categories, cities, selectedCategory, selectedCity, onCategory, onCity }: Props) {
  return (
    <div className="flex gap-2 overflow-x-auto pb-2">
      <label className="sr-only" htmlFor="category">Category</label>
      <select id="category" className="px-3 py-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400" value={selectedCategory} onChange={e => onCategory(e.target.value)}>
        <option value="">All Categories</option>
        {categories.map(c => <option key={c} value={c}>{c}</option>)}
      </select>
      <label className="sr-only" htmlFor="city">City</label>
      <select id="city" className="px-3 py-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400" value={selectedCity} onChange={e => onCity(e.target.value)}>
        <option value="">All Cities</option>
        {cities.map(c => <option key={c} value={c}>{c}</option>)}
      </select>
    </div>
  );
}

