function Yf(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const s in r)if(s!=="default"&&!(s in e)){const i=Object.getOwnPropertyDescriptor(r,s);i&&Object.defineProperty(e,s,i.get?i:{enumerable:!0,get:()=>r[s]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();var Re=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Zf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function ep(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}),n}var Uc={exports:{}},Ei={},Bc={exports:{}},L={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zr=Symbol.for("react.element"),tp=Symbol.for("react.portal"),np=Symbol.for("react.fragment"),rp=Symbol.for("react.strict_mode"),sp=Symbol.for("react.profiler"),ip=Symbol.for("react.provider"),op=Symbol.for("react.context"),ap=Symbol.for("react.forward_ref"),lp=Symbol.for("react.suspense"),up=Symbol.for("react.memo"),cp=Symbol.for("react.lazy"),Ql=Symbol.iterator;function dp(e){return e===null||typeof e!="object"?null:(e=Ql&&e[Ql]||e["@@iterator"],typeof e=="function"?e:null)}var Wc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Hc=Object.assign,Vc={};function Qn(e,t,n){this.props=e,this.context=t,this.refs=Vc,this.updater=n||Wc}Qn.prototype.isReactComponent={};Qn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Qn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function qc(){}qc.prototype=Qn.prototype;function Ka(e,t,n){this.props=e,this.context=t,this.refs=Vc,this.updater=n||Wc}var Ga=Ka.prototype=new qc;Ga.constructor=Ka;Hc(Ga,Qn.prototype);Ga.isPureReactComponent=!0;var Xl=Array.isArray,Kc=Object.prototype.hasOwnProperty,Ja={current:null},Gc={key:!0,ref:!0,__self:!0,__source:!0};function Jc(e,t,n){var r,s={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)Kc.call(t,r)&&!Gc.hasOwnProperty(r)&&(s[r]=t[r]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];s.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)s[r]===void 0&&(s[r]=a[r]);return{$$typeof:Zr,type:e,key:i,ref:o,props:s,_owner:Ja.current}}function hp(e,t){return{$$typeof:Zr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Qa(e){return typeof e=="object"&&e!==null&&e.$$typeof===Zr}function fp(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Yl=/\/+/g;function Xi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?fp(""+e.key):t.toString(36)}function Fs(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case Zr:case tp:o=!0}}if(o)return o=e,s=s(o),e=r===""?"."+Xi(o,0):r,Xl(s)?(n="",e!=null&&(n=e.replace(Yl,"$&/")+"/"),Fs(s,t,n,"",function(u){return u})):s!=null&&(Qa(s)&&(s=hp(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(Yl,"$&/")+"/")+e)),t.push(s)),1;if(o=0,r=r===""?".":r+":",Xl(e))for(var a=0;a<e.length;a++){i=e[a];var l=r+Xi(i,a);o+=Fs(i,t,n,l,s)}else if(l=dp(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=r+Xi(i,a++),o+=Fs(i,t,n,l,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function hs(e,t,n){if(e==null)return e;var r=[],s=0;return Fs(e,r,"","",function(i){return t.call(n,i,s++)}),r}function pp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ye={current:null},zs={transition:null},gp={ReactCurrentDispatcher:ye,ReactCurrentBatchConfig:zs,ReactCurrentOwner:Ja};function Qc(){throw Error("act(...) is not supported in production builds of React.")}L.Children={map:hs,forEach:function(e,t,n){hs(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return hs(e,function(){t++}),t},toArray:function(e){return hs(e,function(t){return t})||[]},only:function(e){if(!Qa(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};L.Component=Qn;L.Fragment=np;L.Profiler=sp;L.PureComponent=Ka;L.StrictMode=rp;L.Suspense=lp;L.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=gp;L.act=Qc;L.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Hc({},e.props),s=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=Ja.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Kc.call(t,l)&&!Gc.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Zr,type:e.type,key:s,ref:i,props:r,_owner:o}};L.createContext=function(e){return e={$$typeof:op,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:ip,_context:e},e.Consumer=e};L.createElement=Jc;L.createFactory=function(e){var t=Jc.bind(null,e);return t.type=e,t};L.createRef=function(){return{current:null}};L.forwardRef=function(e){return{$$typeof:ap,render:e}};L.isValidElement=Qa;L.lazy=function(e){return{$$typeof:cp,_payload:{_status:-1,_result:e},_init:pp}};L.memo=function(e,t){return{$$typeof:up,type:e,compare:t===void 0?null:t}};L.startTransition=function(e){var t=zs.transition;zs.transition={};try{e()}finally{zs.transition=t}};L.unstable_act=Qc;L.useCallback=function(e,t){return ye.current.useCallback(e,t)};L.useContext=function(e){return ye.current.useContext(e)};L.useDebugValue=function(){};L.useDeferredValue=function(e){return ye.current.useDeferredValue(e)};L.useEffect=function(e,t){return ye.current.useEffect(e,t)};L.useId=function(){return ye.current.useId()};L.useImperativeHandle=function(e,t,n){return ye.current.useImperativeHandle(e,t,n)};L.useInsertionEffect=function(e,t){return ye.current.useInsertionEffect(e,t)};L.useLayoutEffect=function(e,t){return ye.current.useLayoutEffect(e,t)};L.useMemo=function(e,t){return ye.current.useMemo(e,t)};L.useReducer=function(e,t,n){return ye.current.useReducer(e,t,n)};L.useRef=function(e){return ye.current.useRef(e)};L.useState=function(e){return ye.current.useState(e)};L.useSyncExternalStore=function(e,t,n){return ye.current.useSyncExternalStore(e,t,n)};L.useTransition=function(){return ye.current.useTransition()};L.version="18.3.1";Bc.exports=L;var k=Bc.exports;const Mi=Zf(k),mp=Yf({__proto__:null,default:Mi},[k]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yp=k,vp=Symbol.for("react.element"),wp=Symbol.for("react.fragment"),xp=Object.prototype.hasOwnProperty,_p=yp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,kp={key:!0,ref:!0,__self:!0,__source:!0};function Xc(e,t,n){var r,s={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)xp.call(t,r)&&!kp.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:vp,type:e,key:i,ref:o,props:s,_owner:_p.current}}Ei.Fragment=wp;Ei.jsx=Xc;Ei.jsxs=Xc;Uc.exports=Ei;var c=Uc.exports,Ro={},Yc={exports:{}},Ne={},Zc={exports:{}},ed={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,j){var E=N.length;N.push(j);e:for(;0<E;){var F=E-1>>>1,X=N[F];if(0<s(X,j))N[F]=j,N[E]=X,E=F;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var j=N[0],E=N.pop();if(E!==j){N[0]=E;e:for(var F=0,X=N.length,Wt=X>>>1;F<Wt;){var nt=2*(F+1)-1,nr=N[nt],Ht=nt+1,ds=N[Ht];if(0>s(nr,E))Ht<X&&0>s(ds,nr)?(N[F]=ds,N[Ht]=E,F=Ht):(N[F]=nr,N[nt]=E,F=nt);else if(Ht<X&&0>s(ds,E))N[F]=ds,N[Ht]=E,F=Ht;else break e}}return j}function s(N,j){var E=N.sortIndex-j.sortIndex;return E!==0?E:N.id-j.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var l=[],u=[],d=1,h=null,f=3,y=!1,v=!1,x=!1,_=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(N){for(var j=n(u);j!==null;){if(j.callback===null)r(u);else if(j.startTime<=N)r(u),j.sortIndex=j.expirationTime,t(l,j);else break;j=n(u)}}function w(N){if(x=!1,g(N),!v)if(n(l)!==null)v=!0,pn(S);else{var j=n(u);j!==null&&Bt(w,j.startTime-N)}}function S(N,j){v=!1,x&&(x=!1,m(M),M=-1),y=!0;var E=f;try{for(g(j),h=n(l);h!==null&&(!(h.expirationTime>j)||N&&!q());){var F=h.callback;if(typeof F=="function"){h.callback=null,f=h.priorityLevel;var X=F(h.expirationTime<=j);j=e.unstable_now(),typeof X=="function"?h.callback=X:h===n(l)&&r(l),g(j)}else r(l);h=n(l)}if(h!==null)var Wt=!0;else{var nt=n(u);nt!==null&&Bt(w,nt.startTime-j),Wt=!1}return Wt}finally{h=null,f=E,y=!1}}var b=!1,P=null,M=-1,O=5,A=-1;function q(){return!(e.unstable_now()-A<O)}function Ge(){if(P!==null){var N=e.unstable_now();A=N;var j=!0;try{j=P(!0,N)}finally{j?Je():(b=!1,P=null)}}else b=!1}var Je;if(typeof p=="function")Je=function(){p(Ge)};else if(typeof MessageChannel<"u"){var gt=new MessageChannel,fn=gt.port2;gt.port1.onmessage=Ge,Je=function(){fn.postMessage(null)}}else Je=function(){_(Ge,0)};function pn(N){P=N,b||(b=!0,Je())}function Bt(N,j){M=_(function(){N(e.unstable_now())},j)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){v||y||(v=!0,pn(S))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(N){switch(f){case 1:case 2:case 3:var j=3;break;default:j=f}var E=f;f=j;try{return N()}finally{f=E}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,j){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var E=f;f=N;try{return j()}finally{f=E}},e.unstable_scheduleCallback=function(N,j,E){var F=e.unstable_now();switch(typeof E=="object"&&E!==null?(E=E.delay,E=typeof E=="number"&&0<E?F+E:F):E=F,N){case 1:var X=-1;break;case 2:X=250;break;case 5:X=**********;break;case 4:X=1e4;break;default:X=5e3}return X=E+X,N={id:d++,callback:j,priorityLevel:N,startTime:E,expirationTime:X,sortIndex:-1},E>F?(N.sortIndex=E,t(u,N),n(l)===null&&N===n(u)&&(x?(m(M),M=-1):x=!0,Bt(w,E-F))):(N.sortIndex=X,t(l,N),v||y||(v=!0,pn(S))),N},e.unstable_shouldYield=q,e.unstable_wrapCallback=function(N){var j=f;return function(){var E=f;f=j;try{return N.apply(this,arguments)}finally{f=E}}}})(ed);Zc.exports=ed;var Sp=Zc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bp=k,Me=Sp;function C(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var td=new Set,$r={};function ln(e,t){Bn(e,t),Bn(e+"Capture",t)}function Bn(e,t){for($r[e]=t,e=0;e<t.length;e++)td.add(t[e])}var ct=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Lo=Object.prototype.hasOwnProperty,Cp=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Zl={},eu={};function Pp(e){return Lo.call(eu,e)?!0:Lo.call(Zl,e)?!1:Cp.test(e)?eu[e]=!0:(Zl[e]=!0,!1)}function jp(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ep(e,t,n,r){if(t===null||typeof t>"u"||jp(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ve(e,t,n,r,s,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var ue={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ue[e]=new ve(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ue[t]=new ve(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ue[e]=new ve(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ue[e]=new ve(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ue[e]=new ve(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ue[e]=new ve(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ue[e]=new ve(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ue[e]=new ve(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ue[e]=new ve(e,5,!1,e.toLowerCase(),null,!1,!1)});var Xa=/[\-:]([a-z])/g;function Ya(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Xa,Ya);ue[t]=new ve(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Xa,Ya);ue[t]=new ve(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Xa,Ya);ue[t]=new ve(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ue[e]=new ve(e,1,!1,e.toLowerCase(),null,!1,!1)});ue.xlinkHref=new ve("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ue[e]=new ve(e,1,!1,e.toLowerCase(),null,!0,!0)});function Za(e,t,n,r){var s=ue.hasOwnProperty(t)?ue[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ep(t,n,s,r)&&(n=null),r||s===null?Pp(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var pt=bp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,fs=Symbol.for("react.element"),bn=Symbol.for("react.portal"),Cn=Symbol.for("react.fragment"),el=Symbol.for("react.strict_mode"),Do=Symbol.for("react.profiler"),nd=Symbol.for("react.provider"),rd=Symbol.for("react.context"),tl=Symbol.for("react.forward_ref"),Fo=Symbol.for("react.suspense"),zo=Symbol.for("react.suspense_list"),nl=Symbol.for("react.memo"),wt=Symbol.for("react.lazy"),sd=Symbol.for("react.offscreen"),tu=Symbol.iterator;function rr(e){return e===null||typeof e!="object"?null:(e=tu&&e[tu]||e["@@iterator"],typeof e=="function"?e:null)}var J=Object.assign,Yi;function pr(e){if(Yi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Yi=t&&t[1]||""}return`
`+Yi+e}var Zi=!1;function eo(e,t){if(!e||Zi)return"";Zi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),i=r.stack.split(`
`),o=s.length-1,a=i.length-1;1<=o&&0<=a&&s[o]!==i[a];)a--;for(;1<=o&&0<=a;o--,a--)if(s[o]!==i[a]){if(o!==1||a!==1)do if(o--,a--,0>a||s[o]!==i[a]){var l=`
`+s[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=o&&0<=a);break}}}finally{Zi=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?pr(e):""}function Mp(e){switch(e.tag){case 5:return pr(e.type);case 16:return pr("Lazy");case 13:return pr("Suspense");case 19:return pr("SuspenseList");case 0:case 2:case 15:return e=eo(e.type,!1),e;case 11:return e=eo(e.type.render,!1),e;case 1:return e=eo(e.type,!0),e;default:return""}}function Uo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Cn:return"Fragment";case bn:return"Portal";case Do:return"Profiler";case el:return"StrictMode";case Fo:return"Suspense";case zo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case rd:return(e.displayName||"Context")+".Consumer";case nd:return(e._context.displayName||"Context")+".Provider";case tl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case nl:return t=e.displayName||null,t!==null?t:Uo(e.type)||"Memo";case wt:t=e._payload,e=e._init;try{return Uo(e(t))}catch{}}return null}function Np(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Uo(t);case 8:return t===el?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Rt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function id(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Tp(e){var t=id(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ps(e){e._valueTracker||(e._valueTracker=Tp(e))}function od(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=id(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Zs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Bo(e,t){var n=t.checked;return J({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function nu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Rt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ad(e,t){t=t.checked,t!=null&&Za(e,"checked",t,!1)}function Wo(e,t){ad(e,t);var n=Rt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ho(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ho(e,t.type,Rt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function ru(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ho(e,t,n){(t!=="number"||Zs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var gr=Array.isArray;function Rn(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Rt(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Vo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(C(91));return J({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function su(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(C(92));if(gr(n)){if(1<n.length)throw Error(C(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Rt(n)}}function ld(e,t){var n=Rt(t.value),r=Rt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function iu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ud(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function qo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ud(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var gs,cd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(gs=gs||document.createElement("div"),gs.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=gs.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Or(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var _r={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ap=["Webkit","ms","Moz","O"];Object.keys(_r).forEach(function(e){Ap.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),_r[t]=_r[e]})});function dd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||_r.hasOwnProperty(e)&&_r[e]?(""+t).trim():t+"px"}function hd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=dd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var $p=J({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ko(e,t){if(t){if($p[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(C(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(C(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(C(61))}if(t.style!=null&&typeof t.style!="object")throw Error(C(62))}}function Go(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Jo=null;function rl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Qo=null,Ln=null,Dn=null;function ou(e){if(e=ns(e)){if(typeof Qo!="function")throw Error(C(280));var t=e.stateNode;t&&(t=Oi(t),Qo(e.stateNode,e.type,t))}}function fd(e){Ln?Dn?Dn.push(e):Dn=[e]:Ln=e}function pd(){if(Ln){var e=Ln,t=Dn;if(Dn=Ln=null,ou(e),t)for(e=0;e<t.length;e++)ou(t[e])}}function gd(e,t){return e(t)}function md(){}var to=!1;function yd(e,t,n){if(to)return e(t,n);to=!0;try{return gd(e,t,n)}finally{to=!1,(Ln!==null||Dn!==null)&&(md(),pd())}}function Ir(e,t){var n=e.stateNode;if(n===null)return null;var r=Oi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(C(231,t,typeof n));return n}var Xo=!1;if(ct)try{var sr={};Object.defineProperty(sr,"passive",{get:function(){Xo=!0}}),window.addEventListener("test",sr,sr),window.removeEventListener("test",sr,sr)}catch{Xo=!1}function Op(e,t,n,r,s,i,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var kr=!1,ei=null,ti=!1,Yo=null,Ip={onError:function(e){kr=!0,ei=e}};function Rp(e,t,n,r,s,i,o,a,l){kr=!1,ei=null,Op.apply(Ip,arguments)}function Lp(e,t,n,r,s,i,o,a,l){if(Rp.apply(this,arguments),kr){if(kr){var u=ei;kr=!1,ei=null}else throw Error(C(198));ti||(ti=!0,Yo=u)}}function un(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function vd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function au(e){if(un(e)!==e)throw Error(C(188))}function Dp(e){var t=e.alternate;if(!t){if(t=un(e),t===null)throw Error(C(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return au(s),e;if(i===r)return au(s),t;i=i.sibling}throw Error(C(188))}if(n.return!==r.return)n=s,r=i;else{for(var o=!1,a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o){for(a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o)throw Error(C(189))}}if(n.alternate!==r)throw Error(C(190))}if(n.tag!==3)throw Error(C(188));return n.stateNode.current===n?e:t}function wd(e){return e=Dp(e),e!==null?xd(e):null}function xd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=xd(e);if(t!==null)return t;e=e.sibling}return null}var _d=Me.unstable_scheduleCallback,lu=Me.unstable_cancelCallback,Fp=Me.unstable_shouldYield,zp=Me.unstable_requestPaint,Y=Me.unstable_now,Up=Me.unstable_getCurrentPriorityLevel,sl=Me.unstable_ImmediatePriority,kd=Me.unstable_UserBlockingPriority,ni=Me.unstable_NormalPriority,Bp=Me.unstable_LowPriority,Sd=Me.unstable_IdlePriority,Ni=null,et=null;function Wp(e){if(et&&typeof et.onCommitFiberRoot=="function")try{et.onCommitFiberRoot(Ni,e,void 0,(e.current.flags&128)===128)}catch{}}var Ve=Math.clz32?Math.clz32:qp,Hp=Math.log,Vp=Math.LN2;function qp(e){return e>>>=0,e===0?32:31-(Hp(e)/Vp|0)|0}var ms=64,ys=4194304;function mr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ri(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~s;a!==0?r=mr(a):(i&=o,i!==0&&(r=mr(i)))}else o=n&~s,o!==0?r=mr(o):i!==0&&(r=mr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ve(t),s=1<<n,r|=e[n],t&=~s;return r}function Kp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Gp(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-Ve(i),a=1<<o,l=s[o];l===-1?(!(a&n)||a&r)&&(s[o]=Kp(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function Zo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function bd(){var e=ms;return ms<<=1,!(ms&4194240)&&(ms=64),e}function no(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function es(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ve(t),e[t]=n}function Jp(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Ve(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function il(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ve(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var z=0;function Cd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Pd,ol,jd,Ed,Md,ea=!1,vs=[],jt=null,Et=null,Mt=null,Rr=new Map,Lr=new Map,_t=[],Qp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function uu(e,t){switch(e){case"focusin":case"focusout":jt=null;break;case"dragenter":case"dragleave":Et=null;break;case"mouseover":case"mouseout":Mt=null;break;case"pointerover":case"pointerout":Rr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Lr.delete(t.pointerId)}}function ir(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=ns(t),t!==null&&ol(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Xp(e,t,n,r,s){switch(t){case"focusin":return jt=ir(jt,e,t,n,r,s),!0;case"dragenter":return Et=ir(Et,e,t,n,r,s),!0;case"mouseover":return Mt=ir(Mt,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return Rr.set(i,ir(Rr.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,Lr.set(i,ir(Lr.get(i)||null,e,t,n,r,s)),!0}return!1}function Nd(e){var t=Xt(e.target);if(t!==null){var n=un(t);if(n!==null){if(t=n.tag,t===13){if(t=vd(n),t!==null){e.blockedOn=t,Md(e.priority,function(){jd(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Us(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ta(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Jo=r,n.target.dispatchEvent(r),Jo=null}else return t=ns(n),t!==null&&ol(t),e.blockedOn=n,!1;t.shift()}return!0}function cu(e,t,n){Us(e)&&n.delete(t)}function Yp(){ea=!1,jt!==null&&Us(jt)&&(jt=null),Et!==null&&Us(Et)&&(Et=null),Mt!==null&&Us(Mt)&&(Mt=null),Rr.forEach(cu),Lr.forEach(cu)}function or(e,t){e.blockedOn===t&&(e.blockedOn=null,ea||(ea=!0,Me.unstable_scheduleCallback(Me.unstable_NormalPriority,Yp)))}function Dr(e){function t(s){return or(s,e)}if(0<vs.length){or(vs[0],e);for(var n=1;n<vs.length;n++){var r=vs[n];r.blockedOn===e&&(r.blockedOn=null)}}for(jt!==null&&or(jt,e),Et!==null&&or(Et,e),Mt!==null&&or(Mt,e),Rr.forEach(t),Lr.forEach(t),n=0;n<_t.length;n++)r=_t[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<_t.length&&(n=_t[0],n.blockedOn===null);)Nd(n),n.blockedOn===null&&_t.shift()}var Fn=pt.ReactCurrentBatchConfig,si=!0;function Zp(e,t,n,r){var s=z,i=Fn.transition;Fn.transition=null;try{z=1,al(e,t,n,r)}finally{z=s,Fn.transition=i}}function e0(e,t,n,r){var s=z,i=Fn.transition;Fn.transition=null;try{z=4,al(e,t,n,r)}finally{z=s,Fn.transition=i}}function al(e,t,n,r){if(si){var s=ta(e,t,n,r);if(s===null)fo(e,t,r,ii,n),uu(e,r);else if(Xp(s,e,t,n,r))r.stopPropagation();else if(uu(e,r),t&4&&-1<Qp.indexOf(e)){for(;s!==null;){var i=ns(s);if(i!==null&&Pd(i),i=ta(e,t,n,r),i===null&&fo(e,t,r,ii,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else fo(e,t,r,null,n)}}var ii=null;function ta(e,t,n,r){if(ii=null,e=rl(r),e=Xt(e),e!==null)if(t=un(e),t===null)e=null;else if(n=t.tag,n===13){if(e=vd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ii=e,null}function Td(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Up()){case sl:return 1;case kd:return 4;case ni:case Bp:return 16;case Sd:return 536870912;default:return 16}default:return 16}}var bt=null,ll=null,Bs=null;function Ad(){if(Bs)return Bs;var e,t=ll,n=t.length,r,s="value"in bt?bt.value:bt.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===s[i-r];r++);return Bs=s.slice(e,1<r?1-r:void 0)}function Ws(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ws(){return!0}function du(){return!1}function Te(e){function t(n,r,s,i,o){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?ws:du,this.isPropagationStopped=du,this}return J(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ws)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ws)},persist:function(){},isPersistent:ws}),t}var Xn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ul=Te(Xn),ts=J({},Xn,{view:0,detail:0}),t0=Te(ts),ro,so,ar,Ti=J({},ts,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:cl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ar&&(ar&&e.type==="mousemove"?(ro=e.screenX-ar.screenX,so=e.screenY-ar.screenY):so=ro=0,ar=e),ro)},movementY:function(e){return"movementY"in e?e.movementY:so}}),hu=Te(Ti),n0=J({},Ti,{dataTransfer:0}),r0=Te(n0),s0=J({},ts,{relatedTarget:0}),io=Te(s0),i0=J({},Xn,{animationName:0,elapsedTime:0,pseudoElement:0}),o0=Te(i0),a0=J({},Xn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),l0=Te(a0),u0=J({},Xn,{data:0}),fu=Te(u0),c0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},d0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},h0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function f0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=h0[e])?!!t[e]:!1}function cl(){return f0}var p0=J({},ts,{key:function(e){if(e.key){var t=c0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ws(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?d0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:cl,charCode:function(e){return e.type==="keypress"?Ws(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ws(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),g0=Te(p0),m0=J({},Ti,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pu=Te(m0),y0=J({},ts,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:cl}),v0=Te(y0),w0=J({},Xn,{propertyName:0,elapsedTime:0,pseudoElement:0}),x0=Te(w0),_0=J({},Ti,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),k0=Te(_0),S0=[9,13,27,32],dl=ct&&"CompositionEvent"in window,Sr=null;ct&&"documentMode"in document&&(Sr=document.documentMode);var b0=ct&&"TextEvent"in window&&!Sr,$d=ct&&(!dl||Sr&&8<Sr&&11>=Sr),gu=" ",mu=!1;function Od(e,t){switch(e){case"keyup":return S0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Id(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Pn=!1;function C0(e,t){switch(e){case"compositionend":return Id(t);case"keypress":return t.which!==32?null:(mu=!0,gu);case"textInput":return e=t.data,e===gu&&mu?null:e;default:return null}}function P0(e,t){if(Pn)return e==="compositionend"||!dl&&Od(e,t)?(e=Ad(),Bs=ll=bt=null,Pn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return $d&&t.locale!=="ko"?null:t.data;default:return null}}var j0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!j0[e.type]:t==="textarea"}function Rd(e,t,n,r){fd(r),t=oi(t,"onChange"),0<t.length&&(n=new ul("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var br=null,Fr=null;function E0(e){Kd(e,0)}function Ai(e){var t=Mn(e);if(od(t))return e}function M0(e,t){if(e==="change")return t}var Ld=!1;if(ct){var oo;if(ct){var ao="oninput"in document;if(!ao){var vu=document.createElement("div");vu.setAttribute("oninput","return;"),ao=typeof vu.oninput=="function"}oo=ao}else oo=!1;Ld=oo&&(!document.documentMode||9<document.documentMode)}function wu(){br&&(br.detachEvent("onpropertychange",Dd),Fr=br=null)}function Dd(e){if(e.propertyName==="value"&&Ai(Fr)){var t=[];Rd(t,Fr,e,rl(e)),yd(E0,t)}}function N0(e,t,n){e==="focusin"?(wu(),br=t,Fr=n,br.attachEvent("onpropertychange",Dd)):e==="focusout"&&wu()}function T0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ai(Fr)}function A0(e,t){if(e==="click")return Ai(t)}function $0(e,t){if(e==="input"||e==="change")return Ai(t)}function O0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ke=typeof Object.is=="function"?Object.is:O0;function zr(e,t){if(Ke(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Lo.call(t,s)||!Ke(e[s],t[s]))return!1}return!0}function xu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function _u(e,t){var n=xu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=xu(n)}}function Fd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Fd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function zd(){for(var e=window,t=Zs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Zs(e.document)}return t}function hl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function I0(e){var t=zd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Fd(n.ownerDocument.documentElement,n)){if(r!==null&&hl(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=_u(n,i);var o=_u(n,r);s&&o&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var R0=ct&&"documentMode"in document&&11>=document.documentMode,jn=null,na=null,Cr=null,ra=!1;function ku(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ra||jn==null||jn!==Zs(r)||(r=jn,"selectionStart"in r&&hl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Cr&&zr(Cr,r)||(Cr=r,r=oi(na,"onSelect"),0<r.length&&(t=new ul("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=jn)))}function xs(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var En={animationend:xs("Animation","AnimationEnd"),animationiteration:xs("Animation","AnimationIteration"),animationstart:xs("Animation","AnimationStart"),transitionend:xs("Transition","TransitionEnd")},lo={},Ud={};ct&&(Ud=document.createElement("div").style,"AnimationEvent"in window||(delete En.animationend.animation,delete En.animationiteration.animation,delete En.animationstart.animation),"TransitionEvent"in window||delete En.transitionend.transition);function $i(e){if(lo[e])return lo[e];if(!En[e])return e;var t=En[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ud)return lo[e]=t[n];return e}var Bd=$i("animationend"),Wd=$i("animationiteration"),Hd=$i("animationstart"),Vd=$i("transitionend"),qd=new Map,Su="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Dt(e,t){qd.set(e,t),ln(t,[e])}for(var uo=0;uo<Su.length;uo++){var co=Su[uo],L0=co.toLowerCase(),D0=co[0].toUpperCase()+co.slice(1);Dt(L0,"on"+D0)}Dt(Bd,"onAnimationEnd");Dt(Wd,"onAnimationIteration");Dt(Hd,"onAnimationStart");Dt("dblclick","onDoubleClick");Dt("focusin","onFocus");Dt("focusout","onBlur");Dt(Vd,"onTransitionEnd");Bn("onMouseEnter",["mouseout","mouseover"]);Bn("onMouseLeave",["mouseout","mouseover"]);Bn("onPointerEnter",["pointerout","pointerover"]);Bn("onPointerLeave",["pointerout","pointerover"]);ln("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));ln("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));ln("onBeforeInput",["compositionend","keypress","textInput","paste"]);ln("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));ln("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));ln("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var yr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),F0=new Set("cancel close invalid load scroll toggle".split(" ").concat(yr));function bu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Lp(r,t,void 0,e),e.currentTarget=null}function Kd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&s.isPropagationStopped())break e;bu(s,a,u),i=l}else for(o=0;o<r.length;o++){if(a=r[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&s.isPropagationStopped())break e;bu(s,a,u),i=l}}}if(ti)throw e=Yo,ti=!1,Yo=null,e}function W(e,t){var n=t[la];n===void 0&&(n=t[la]=new Set);var r=e+"__bubble";n.has(r)||(Gd(t,e,2,!1),n.add(r))}function ho(e,t,n){var r=0;t&&(r|=4),Gd(n,e,r,t)}var _s="_reactListening"+Math.random().toString(36).slice(2);function Ur(e){if(!e[_s]){e[_s]=!0,td.forEach(function(n){n!=="selectionchange"&&(F0.has(n)||ho(n,!1,e),ho(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[_s]||(t[_s]=!0,ho("selectionchange",!1,t))}}function Gd(e,t,n,r){switch(Td(t)){case 1:var s=Zp;break;case 4:s=e0;break;default:s=al}n=s.bind(null,t,n,e),s=void 0,!Xo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function fo(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===s||l.nodeType===8&&l.parentNode===s))return;o=o.return}for(;a!==null;){if(o=Xt(a),o===null)return;if(l=o.tag,l===5||l===6){r=i=o;continue e}a=a.parentNode}}r=r.return}yd(function(){var u=i,d=rl(n),h=[];e:{var f=qd.get(e);if(f!==void 0){var y=ul,v=e;switch(e){case"keypress":if(Ws(n)===0)break e;case"keydown":case"keyup":y=g0;break;case"focusin":v="focus",y=io;break;case"focusout":v="blur",y=io;break;case"beforeblur":case"afterblur":y=io;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=hu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=r0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=v0;break;case Bd:case Wd:case Hd:y=o0;break;case Vd:y=x0;break;case"scroll":y=t0;break;case"wheel":y=k0;break;case"copy":case"cut":case"paste":y=l0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=pu}var x=(t&4)!==0,_=!x&&e==="scroll",m=x?f!==null?f+"Capture":null:f;x=[];for(var p=u,g;p!==null;){g=p;var w=g.stateNode;if(g.tag===5&&w!==null&&(g=w,m!==null&&(w=Ir(p,m),w!=null&&x.push(Br(p,w,g)))),_)break;p=p.return}0<x.length&&(f=new y(f,v,null,n,d),h.push({event:f,listeners:x}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",f&&n!==Jo&&(v=n.relatedTarget||n.fromElement)&&(Xt(v)||v[dt]))break e;if((y||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,y?(v=n.relatedTarget||n.toElement,y=u,v=v?Xt(v):null,v!==null&&(_=un(v),v!==_||v.tag!==5&&v.tag!==6)&&(v=null)):(y=null,v=u),y!==v)){if(x=hu,w="onMouseLeave",m="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(x=pu,w="onPointerLeave",m="onPointerEnter",p="pointer"),_=y==null?f:Mn(y),g=v==null?f:Mn(v),f=new x(w,p+"leave",y,n,d),f.target=_,f.relatedTarget=g,w=null,Xt(d)===u&&(x=new x(m,p+"enter",v,n,d),x.target=g,x.relatedTarget=_,w=x),_=w,y&&v)t:{for(x=y,m=v,p=0,g=x;g;g=gn(g))p++;for(g=0,w=m;w;w=gn(w))g++;for(;0<p-g;)x=gn(x),p--;for(;0<g-p;)m=gn(m),g--;for(;p--;){if(x===m||m!==null&&x===m.alternate)break t;x=gn(x),m=gn(m)}x=null}else x=null;y!==null&&Cu(h,f,y,x,!1),v!==null&&_!==null&&Cu(h,_,v,x,!0)}}e:{if(f=u?Mn(u):window,y=f.nodeName&&f.nodeName.toLowerCase(),y==="select"||y==="input"&&f.type==="file")var S=M0;else if(yu(f))if(Ld)S=$0;else{S=T0;var b=N0}else(y=f.nodeName)&&y.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(S=A0);if(S&&(S=S(e,u))){Rd(h,S,n,d);break e}b&&b(e,f,u),e==="focusout"&&(b=f._wrapperState)&&b.controlled&&f.type==="number"&&Ho(f,"number",f.value)}switch(b=u?Mn(u):window,e){case"focusin":(yu(b)||b.contentEditable==="true")&&(jn=b,na=u,Cr=null);break;case"focusout":Cr=na=jn=null;break;case"mousedown":ra=!0;break;case"contextmenu":case"mouseup":case"dragend":ra=!1,ku(h,n,d);break;case"selectionchange":if(R0)break;case"keydown":case"keyup":ku(h,n,d)}var P;if(dl)e:{switch(e){case"compositionstart":var M="onCompositionStart";break e;case"compositionend":M="onCompositionEnd";break e;case"compositionupdate":M="onCompositionUpdate";break e}M=void 0}else Pn?Od(e,n)&&(M="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(M="onCompositionStart");M&&($d&&n.locale!=="ko"&&(Pn||M!=="onCompositionStart"?M==="onCompositionEnd"&&Pn&&(P=Ad()):(bt=d,ll="value"in bt?bt.value:bt.textContent,Pn=!0)),b=oi(u,M),0<b.length&&(M=new fu(M,e,null,n,d),h.push({event:M,listeners:b}),P?M.data=P:(P=Id(n),P!==null&&(M.data=P)))),(P=b0?C0(e,n):P0(e,n))&&(u=oi(u,"onBeforeInput"),0<u.length&&(d=new fu("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:u}),d.data=P))}Kd(h,t)})}function Br(e,t,n){return{instance:e,listener:t,currentTarget:n}}function oi(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Ir(e,n),i!=null&&r.unshift(Br(e,i,s)),i=Ir(e,t),i!=null&&r.push(Br(e,i,s))),e=e.return}return r}function gn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Cu(e,t,n,r,s){for(var i=t._reactName,o=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,s?(l=Ir(n,i),l!=null&&o.unshift(Br(n,l,a))):s||(l=Ir(n,i),l!=null&&o.push(Br(n,l,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var z0=/\r\n?/g,U0=/\u0000|\uFFFD/g;function Pu(e){return(typeof e=="string"?e:""+e).replace(z0,`
`).replace(U0,"")}function ks(e,t,n){if(t=Pu(t),Pu(e)!==t&&n)throw Error(C(425))}function ai(){}var sa=null,ia=null;function oa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var aa=typeof setTimeout=="function"?setTimeout:void 0,B0=typeof clearTimeout=="function"?clearTimeout:void 0,ju=typeof Promise=="function"?Promise:void 0,W0=typeof queueMicrotask=="function"?queueMicrotask:typeof ju<"u"?function(e){return ju.resolve(null).then(e).catch(H0)}:aa;function H0(e){setTimeout(function(){throw e})}function po(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),Dr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);Dr(t)}function Nt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Eu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Yn=Math.random().toString(36).slice(2),Ze="__reactFiber$"+Yn,Wr="__reactProps$"+Yn,dt="__reactContainer$"+Yn,la="__reactEvents$"+Yn,V0="__reactListeners$"+Yn,q0="__reactHandles$"+Yn;function Xt(e){var t=e[Ze];if(t)return t;for(var n=e.parentNode;n;){if(t=n[dt]||n[Ze]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Eu(e);e!==null;){if(n=e[Ze])return n;e=Eu(e)}return t}e=n,n=e.parentNode}return null}function ns(e){return e=e[Ze]||e[dt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Mn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(C(33))}function Oi(e){return e[Wr]||null}var ua=[],Nn=-1;function Ft(e){return{current:e}}function H(e){0>Nn||(e.current=ua[Nn],ua[Nn]=null,Nn--)}function B(e,t){Nn++,ua[Nn]=e.current,e.current=t}var Lt={},fe=Ft(Lt),ke=Ft(!1),nn=Lt;function Wn(e,t){var n=e.type.contextTypes;if(!n)return Lt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Se(e){return e=e.childContextTypes,e!=null}function li(){H(ke),H(fe)}function Mu(e,t,n){if(fe.current!==Lt)throw Error(C(168));B(fe,t),B(ke,n)}function Jd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(C(108,Np(e)||"Unknown",s));return J({},n,r)}function ui(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Lt,nn=fe.current,B(fe,e),B(ke,ke.current),!0}function Nu(e,t,n){var r=e.stateNode;if(!r)throw Error(C(169));n?(e=Jd(e,t,nn),r.__reactInternalMemoizedMergedChildContext=e,H(ke),H(fe),B(fe,e)):H(ke),B(ke,n)}var ot=null,Ii=!1,go=!1;function Qd(e){ot===null?ot=[e]:ot.push(e)}function K0(e){Ii=!0,Qd(e)}function zt(){if(!go&&ot!==null){go=!0;var e=0,t=z;try{var n=ot;for(z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}ot=null,Ii=!1}catch(s){throw ot!==null&&(ot=ot.slice(e+1)),_d(sl,zt),s}finally{z=t,go=!1}}return null}var Tn=[],An=0,ci=null,di=0,Ae=[],$e=0,rn=null,at=1,lt="";function Kt(e,t){Tn[An++]=di,Tn[An++]=ci,ci=e,di=t}function Xd(e,t,n){Ae[$e++]=at,Ae[$e++]=lt,Ae[$e++]=rn,rn=e;var r=at;e=lt;var s=32-Ve(r)-1;r&=~(1<<s),n+=1;var i=32-Ve(t)+s;if(30<i){var o=s-s%5;i=(r&(1<<o)-1).toString(32),r>>=o,s-=o,at=1<<32-Ve(t)+s|n<<s|r,lt=i+e}else at=1<<i|n<<s|r,lt=e}function fl(e){e.return!==null&&(Kt(e,1),Xd(e,1,0))}function pl(e){for(;e===ci;)ci=Tn[--An],Tn[An]=null,di=Tn[--An],Tn[An]=null;for(;e===rn;)rn=Ae[--$e],Ae[$e]=null,lt=Ae[--$e],Ae[$e]=null,at=Ae[--$e],Ae[$e]=null}var Ee=null,je=null,V=!1,He=null;function Yd(e,t){var n=Oe(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Tu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ee=e,je=Nt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ee=e,je=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=rn!==null?{id:at,overflow:lt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Oe(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ee=e,je=null,!0):!1;default:return!1}}function ca(e){return(e.mode&1)!==0&&(e.flags&128)===0}function da(e){if(V){var t=je;if(t){var n=t;if(!Tu(e,t)){if(ca(e))throw Error(C(418));t=Nt(n.nextSibling);var r=Ee;t&&Tu(e,t)?Yd(r,n):(e.flags=e.flags&-4097|2,V=!1,Ee=e)}}else{if(ca(e))throw Error(C(418));e.flags=e.flags&-4097|2,V=!1,Ee=e}}}function Au(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ee=e}function Ss(e){if(e!==Ee)return!1;if(!V)return Au(e),V=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!oa(e.type,e.memoizedProps)),t&&(t=je)){if(ca(e))throw Zd(),Error(C(418));for(;t;)Yd(e,t),t=Nt(t.nextSibling)}if(Au(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(C(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){je=Nt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}je=null}}else je=Ee?Nt(e.stateNode.nextSibling):null;return!0}function Zd(){for(var e=je;e;)e=Nt(e.nextSibling)}function Hn(){je=Ee=null,V=!1}function gl(e){He===null?He=[e]:He.push(e)}var G0=pt.ReactCurrentBatchConfig;function lr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(C(309));var r=n.stateNode}if(!r)throw Error(C(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var a=s.refs;o===null?delete a[i]:a[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(C(284));if(!n._owner)throw Error(C(290,e))}return e}function bs(e,t){throw e=Object.prototype.toString.call(t),Error(C(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function $u(e){var t=e._init;return t(e._payload)}function eh(e){function t(m,p){if(e){var g=m.deletions;g===null?(m.deletions=[p],m.flags|=16):g.push(p)}}function n(m,p){if(!e)return null;for(;p!==null;)t(m,p),p=p.sibling;return null}function r(m,p){for(m=new Map;p!==null;)p.key!==null?m.set(p.key,p):m.set(p.index,p),p=p.sibling;return m}function s(m,p){return m=Ot(m,p),m.index=0,m.sibling=null,m}function i(m,p,g){return m.index=g,e?(g=m.alternate,g!==null?(g=g.index,g<p?(m.flags|=2,p):g):(m.flags|=2,p)):(m.flags|=1048576,p)}function o(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,p,g,w){return p===null||p.tag!==6?(p=ko(g,m.mode,w),p.return=m,p):(p=s(p,g),p.return=m,p)}function l(m,p,g,w){var S=g.type;return S===Cn?d(m,p,g.props.children,w,g.key):p!==null&&(p.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===wt&&$u(S)===p.type)?(w=s(p,g.props),w.ref=lr(m,p,g),w.return=m,w):(w=Qs(g.type,g.key,g.props,null,m.mode,w),w.ref=lr(m,p,g),w.return=m,w)}function u(m,p,g,w){return p===null||p.tag!==4||p.stateNode.containerInfo!==g.containerInfo||p.stateNode.implementation!==g.implementation?(p=So(g,m.mode,w),p.return=m,p):(p=s(p,g.children||[]),p.return=m,p)}function d(m,p,g,w,S){return p===null||p.tag!==7?(p=tn(g,m.mode,w,S),p.return=m,p):(p=s(p,g),p.return=m,p)}function h(m,p,g){if(typeof p=="string"&&p!==""||typeof p=="number")return p=ko(""+p,m.mode,g),p.return=m,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case fs:return g=Qs(p.type,p.key,p.props,null,m.mode,g),g.ref=lr(m,null,p),g.return=m,g;case bn:return p=So(p,m.mode,g),p.return=m,p;case wt:var w=p._init;return h(m,w(p._payload),g)}if(gr(p)||rr(p))return p=tn(p,m.mode,g,null),p.return=m,p;bs(m,p)}return null}function f(m,p,g,w){var S=p!==null?p.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return S!==null?null:a(m,p,""+g,w);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case fs:return g.key===S?l(m,p,g,w):null;case bn:return g.key===S?u(m,p,g,w):null;case wt:return S=g._init,f(m,p,S(g._payload),w)}if(gr(g)||rr(g))return S!==null?null:d(m,p,g,w,null);bs(m,g)}return null}function y(m,p,g,w,S){if(typeof w=="string"&&w!==""||typeof w=="number")return m=m.get(g)||null,a(p,m,""+w,S);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case fs:return m=m.get(w.key===null?g:w.key)||null,l(p,m,w,S);case bn:return m=m.get(w.key===null?g:w.key)||null,u(p,m,w,S);case wt:var b=w._init;return y(m,p,g,b(w._payload),S)}if(gr(w)||rr(w))return m=m.get(g)||null,d(p,m,w,S,null);bs(p,w)}return null}function v(m,p,g,w){for(var S=null,b=null,P=p,M=p=0,O=null;P!==null&&M<g.length;M++){P.index>M?(O=P,P=null):O=P.sibling;var A=f(m,P,g[M],w);if(A===null){P===null&&(P=O);break}e&&P&&A.alternate===null&&t(m,P),p=i(A,p,M),b===null?S=A:b.sibling=A,b=A,P=O}if(M===g.length)return n(m,P),V&&Kt(m,M),S;if(P===null){for(;M<g.length;M++)P=h(m,g[M],w),P!==null&&(p=i(P,p,M),b===null?S=P:b.sibling=P,b=P);return V&&Kt(m,M),S}for(P=r(m,P);M<g.length;M++)O=y(P,m,M,g[M],w),O!==null&&(e&&O.alternate!==null&&P.delete(O.key===null?M:O.key),p=i(O,p,M),b===null?S=O:b.sibling=O,b=O);return e&&P.forEach(function(q){return t(m,q)}),V&&Kt(m,M),S}function x(m,p,g,w){var S=rr(g);if(typeof S!="function")throw Error(C(150));if(g=S.call(g),g==null)throw Error(C(151));for(var b=S=null,P=p,M=p=0,O=null,A=g.next();P!==null&&!A.done;M++,A=g.next()){P.index>M?(O=P,P=null):O=P.sibling;var q=f(m,P,A.value,w);if(q===null){P===null&&(P=O);break}e&&P&&q.alternate===null&&t(m,P),p=i(q,p,M),b===null?S=q:b.sibling=q,b=q,P=O}if(A.done)return n(m,P),V&&Kt(m,M),S;if(P===null){for(;!A.done;M++,A=g.next())A=h(m,A.value,w),A!==null&&(p=i(A,p,M),b===null?S=A:b.sibling=A,b=A);return V&&Kt(m,M),S}for(P=r(m,P);!A.done;M++,A=g.next())A=y(P,m,M,A.value,w),A!==null&&(e&&A.alternate!==null&&P.delete(A.key===null?M:A.key),p=i(A,p,M),b===null?S=A:b.sibling=A,b=A);return e&&P.forEach(function(Ge){return t(m,Ge)}),V&&Kt(m,M),S}function _(m,p,g,w){if(typeof g=="object"&&g!==null&&g.type===Cn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case fs:e:{for(var S=g.key,b=p;b!==null;){if(b.key===S){if(S=g.type,S===Cn){if(b.tag===7){n(m,b.sibling),p=s(b,g.props.children),p.return=m,m=p;break e}}else if(b.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===wt&&$u(S)===b.type){n(m,b.sibling),p=s(b,g.props),p.ref=lr(m,b,g),p.return=m,m=p;break e}n(m,b);break}else t(m,b);b=b.sibling}g.type===Cn?(p=tn(g.props.children,m.mode,w,g.key),p.return=m,m=p):(w=Qs(g.type,g.key,g.props,null,m.mode,w),w.ref=lr(m,p,g),w.return=m,m=w)}return o(m);case bn:e:{for(b=g.key;p!==null;){if(p.key===b)if(p.tag===4&&p.stateNode.containerInfo===g.containerInfo&&p.stateNode.implementation===g.implementation){n(m,p.sibling),p=s(p,g.children||[]),p.return=m,m=p;break e}else{n(m,p);break}else t(m,p);p=p.sibling}p=So(g,m.mode,w),p.return=m,m=p}return o(m);case wt:return b=g._init,_(m,p,b(g._payload),w)}if(gr(g))return v(m,p,g,w);if(rr(g))return x(m,p,g,w);bs(m,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,p!==null&&p.tag===6?(n(m,p.sibling),p=s(p,g),p.return=m,m=p):(n(m,p),p=ko(g,m.mode,w),p.return=m,m=p),o(m)):n(m,p)}return _}var Vn=eh(!0),th=eh(!1),hi=Ft(null),fi=null,$n=null,ml=null;function yl(){ml=$n=fi=null}function vl(e){var t=hi.current;H(hi),e._currentValue=t}function ha(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function zn(e,t){fi=e,ml=$n=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(_e=!0),e.firstContext=null)}function Le(e){var t=e._currentValue;if(ml!==e)if(e={context:e,memoizedValue:t,next:null},$n===null){if(fi===null)throw Error(C(308));$n=e,fi.dependencies={lanes:0,firstContext:e}}else $n=$n.next=e;return t}var Yt=null;function wl(e){Yt===null?Yt=[e]:Yt.push(e)}function nh(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,wl(t)):(n.next=s.next,s.next=n),t.interleaved=n,ht(e,r)}function ht(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var xt=!1;function xl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function rh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ut(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Tt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,D&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,ht(e,n)}return s=r.interleaved,s===null?(t.next=t,wl(r)):(t.next=s.next,s.next=t),r.interleaved=t,ht(e,n)}function Hs(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,il(e,n)}}function Ou(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function pi(e,t,n,r){var s=e.updateQueue;xt=!1;var i=s.firstBaseUpdate,o=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?i=u:o.next=u,o=l;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==o&&(a===null?d.firstBaseUpdate=u:a.next=u,d.lastBaseUpdate=l))}if(i!==null){var h=s.baseState;o=0,d=u=l=null,a=i;do{var f=a.lane,y=a.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:y,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=e,x=a;switch(f=t,y=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){h=v.call(y,h,f);break e}h=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,f=typeof v=="function"?v.call(y,h,f):v,f==null)break e;h=J({},h,f);break e;case 2:xt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=s.effects,f===null?s.effects=[a]:f.push(a))}else y={eventTime:y,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(u=d=y,l=h):d=d.next=y,o|=f;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;f=a,a=f.next,f.next=null,s.lastBaseUpdate=f,s.shared.pending=null}}while(!0);if(d===null&&(l=h),s.baseState=l,s.firstBaseUpdate=u,s.lastBaseUpdate=d,t=s.shared.interleaved,t!==null){s=t;do o|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);on|=o,e.lanes=o,e.memoizedState=h}}function Iu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(C(191,s));s.call(r)}}}var rs={},tt=Ft(rs),Hr=Ft(rs),Vr=Ft(rs);function Zt(e){if(e===rs)throw Error(C(174));return e}function _l(e,t){switch(B(Vr,t),B(Hr,e),B(tt,rs),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:qo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=qo(t,e)}H(tt),B(tt,t)}function qn(){H(tt),H(Hr),H(Vr)}function sh(e){Zt(Vr.current);var t=Zt(tt.current),n=qo(t,e.type);t!==n&&(B(Hr,e),B(tt,n))}function kl(e){Hr.current===e&&(H(tt),H(Hr))}var K=Ft(0);function gi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var mo=[];function Sl(){for(var e=0;e<mo.length;e++)mo[e]._workInProgressVersionPrimary=null;mo.length=0}var Vs=pt.ReactCurrentDispatcher,yo=pt.ReactCurrentBatchConfig,sn=0,G=null,re=null,ie=null,mi=!1,Pr=!1,qr=0,J0=0;function ce(){throw Error(C(321))}function bl(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ke(e[n],t[n]))return!1;return!0}function Cl(e,t,n,r,s,i){if(sn=i,G=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Vs.current=e===null||e.memoizedState===null?Z0:eg,e=n(r,s),Pr){i=0;do{if(Pr=!1,qr=0,25<=i)throw Error(C(301));i+=1,ie=re=null,t.updateQueue=null,Vs.current=tg,e=n(r,s)}while(Pr)}if(Vs.current=yi,t=re!==null&&re.next!==null,sn=0,ie=re=G=null,mi=!1,t)throw Error(C(300));return e}function Pl(){var e=qr!==0;return qr=0,e}function Xe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ie===null?G.memoizedState=ie=e:ie=ie.next=e,ie}function De(){if(re===null){var e=G.alternate;e=e!==null?e.memoizedState:null}else e=re.next;var t=ie===null?G.memoizedState:ie.next;if(t!==null)ie=t,re=e;else{if(e===null)throw Error(C(310));re=e,e={memoizedState:re.memoizedState,baseState:re.baseState,baseQueue:re.baseQueue,queue:re.queue,next:null},ie===null?G.memoizedState=ie=e:ie=ie.next=e}return ie}function Kr(e,t){return typeof t=="function"?t(e):t}function vo(e){var t=De(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=re,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var o=s.next;s.next=i.next,i.next=o}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var a=o=null,l=null,u=i;do{var d=u.lane;if((sn&d)===d)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var h={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=h,o=r):l=l.next=h,G.lanes|=d,on|=d}u=u.next}while(u!==null&&u!==i);l===null?o=r:l.next=a,Ke(r,t.memoizedState)||(_e=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,G.lanes|=i,on|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function wo(e){var t=De(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do i=e(i,o.action),o=o.next;while(o!==s);Ke(i,t.memoizedState)||(_e=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function ih(){}function oh(e,t){var n=G,r=De(),s=t(),i=!Ke(r.memoizedState,s);if(i&&(r.memoizedState=s,_e=!0),r=r.queue,jl(uh.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ie!==null&&ie.memoizedState.tag&1){if(n.flags|=2048,Gr(9,lh.bind(null,n,r,s,t),void 0,null),oe===null)throw Error(C(349));sn&30||ah(n,t,s)}return s}function ah(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function lh(e,t,n,r){t.value=n,t.getSnapshot=r,ch(t)&&dh(e)}function uh(e,t,n){return n(function(){ch(t)&&dh(e)})}function ch(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ke(e,n)}catch{return!0}}function dh(e){var t=ht(e,1);t!==null&&qe(t,e,1,-1)}function Ru(e){var t=Xe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Kr,lastRenderedState:e},t.queue=e,e=e.dispatch=Y0.bind(null,G,e),[t.memoizedState,e]}function Gr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function hh(){return De().memoizedState}function qs(e,t,n,r){var s=Xe();G.flags|=e,s.memoizedState=Gr(1|t,n,void 0,r===void 0?null:r)}function Ri(e,t,n,r){var s=De();r=r===void 0?null:r;var i=void 0;if(re!==null){var o=re.memoizedState;if(i=o.destroy,r!==null&&bl(r,o.deps)){s.memoizedState=Gr(t,n,i,r);return}}G.flags|=e,s.memoizedState=Gr(1|t,n,i,r)}function Lu(e,t){return qs(8390656,8,e,t)}function jl(e,t){return Ri(2048,8,e,t)}function fh(e,t){return Ri(4,2,e,t)}function ph(e,t){return Ri(4,4,e,t)}function gh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function mh(e,t,n){return n=n!=null?n.concat([e]):null,Ri(4,4,gh.bind(null,t,e),n)}function El(){}function yh(e,t){var n=De();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&bl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function vh(e,t){var n=De();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&bl(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function wh(e,t,n){return sn&21?(Ke(n,t)||(n=bd(),G.lanes|=n,on|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,_e=!0),e.memoizedState=n)}function Q0(e,t){var n=z;z=n!==0&&4>n?n:4,e(!0);var r=yo.transition;yo.transition={};try{e(!1),t()}finally{z=n,yo.transition=r}}function xh(){return De().memoizedState}function X0(e,t,n){var r=$t(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},_h(e))kh(t,n);else if(n=nh(e,t,n,r),n!==null){var s=me();qe(n,e,r,s),Sh(n,t,r)}}function Y0(e,t,n){var r=$t(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(_h(e))kh(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,a=i(o,n);if(s.hasEagerState=!0,s.eagerState=a,Ke(a,o)){var l=t.interleaved;l===null?(s.next=s,wl(t)):(s.next=l.next,l.next=s),t.interleaved=s;return}}catch{}finally{}n=nh(e,t,s,r),n!==null&&(s=me(),qe(n,e,r,s),Sh(n,t,r))}}function _h(e){var t=e.alternate;return e===G||t!==null&&t===G}function kh(e,t){Pr=mi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Sh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,il(e,n)}}var yi={readContext:Le,useCallback:ce,useContext:ce,useEffect:ce,useImperativeHandle:ce,useInsertionEffect:ce,useLayoutEffect:ce,useMemo:ce,useReducer:ce,useRef:ce,useState:ce,useDebugValue:ce,useDeferredValue:ce,useTransition:ce,useMutableSource:ce,useSyncExternalStore:ce,useId:ce,unstable_isNewReconciler:!1},Z0={readContext:Le,useCallback:function(e,t){return Xe().memoizedState=[e,t===void 0?null:t],e},useContext:Le,useEffect:Lu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,qs(4194308,4,gh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return qs(4194308,4,e,t)},useInsertionEffect:function(e,t){return qs(4,2,e,t)},useMemo:function(e,t){var n=Xe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Xe();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=X0.bind(null,G,e),[r.memoizedState,e]},useRef:function(e){var t=Xe();return e={current:e},t.memoizedState=e},useState:Ru,useDebugValue:El,useDeferredValue:function(e){return Xe().memoizedState=e},useTransition:function(){var e=Ru(!1),t=e[0];return e=Q0.bind(null,e[1]),Xe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=G,s=Xe();if(V){if(n===void 0)throw Error(C(407));n=n()}else{if(n=t(),oe===null)throw Error(C(349));sn&30||ah(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,Lu(uh.bind(null,r,i,e),[e]),r.flags|=2048,Gr(9,lh.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Xe(),t=oe.identifierPrefix;if(V){var n=lt,r=at;n=(r&~(1<<32-Ve(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=qr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=J0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},eg={readContext:Le,useCallback:yh,useContext:Le,useEffect:jl,useImperativeHandle:mh,useInsertionEffect:fh,useLayoutEffect:ph,useMemo:vh,useReducer:vo,useRef:hh,useState:function(){return vo(Kr)},useDebugValue:El,useDeferredValue:function(e){var t=De();return wh(t,re.memoizedState,e)},useTransition:function(){var e=vo(Kr)[0],t=De().memoizedState;return[e,t]},useMutableSource:ih,useSyncExternalStore:oh,useId:xh,unstable_isNewReconciler:!1},tg={readContext:Le,useCallback:yh,useContext:Le,useEffect:jl,useImperativeHandle:mh,useInsertionEffect:fh,useLayoutEffect:ph,useMemo:vh,useReducer:wo,useRef:hh,useState:function(){return wo(Kr)},useDebugValue:El,useDeferredValue:function(e){var t=De();return re===null?t.memoizedState=e:wh(t,re.memoizedState,e)},useTransition:function(){var e=wo(Kr)[0],t=De().memoizedState;return[e,t]},useMutableSource:ih,useSyncExternalStore:oh,useId:xh,unstable_isNewReconciler:!1};function Ue(e,t){if(e&&e.defaultProps){t=J({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function fa(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:J({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Li={isMounted:function(e){return(e=e._reactInternals)?un(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=me(),s=$t(e),i=ut(r,s);i.payload=t,n!=null&&(i.callback=n),t=Tt(e,i,s),t!==null&&(qe(t,e,s,r),Hs(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=me(),s=$t(e),i=ut(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Tt(e,i,s),t!==null&&(qe(t,e,s,r),Hs(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=me(),r=$t(e),s=ut(n,r);s.tag=2,t!=null&&(s.callback=t),t=Tt(e,s,r),t!==null&&(qe(t,e,r,n),Hs(t,e,r))}};function Du(e,t,n,r,s,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!zr(n,r)||!zr(s,i):!0}function bh(e,t,n){var r=!1,s=Lt,i=t.contextType;return typeof i=="object"&&i!==null?i=Le(i):(s=Se(t)?nn:fe.current,r=t.contextTypes,i=(r=r!=null)?Wn(e,s):Lt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Li,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function Fu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Li.enqueueReplaceState(t,t.state,null)}function pa(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},xl(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=Le(i):(i=Se(t)?nn:fe.current,s.context=Wn(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(fa(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&Li.enqueueReplaceState(s,s.state,null),pi(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Kn(e,t){try{var n="",r=t;do n+=Mp(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function xo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ga(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var ng=typeof WeakMap=="function"?WeakMap:Map;function Ch(e,t,n){n=ut(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){wi||(wi=!0,Ca=r),ga(e,t)},n}function Ph(e,t,n){n=ut(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){ga(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){ga(e,t),typeof r!="function"&&(At===null?At=new Set([this]):At.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function zu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new ng;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=mg.bind(null,e,t,n),t.then(e,e))}function Uu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Bu(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ut(-1,1),t.tag=2,Tt(n,t,1))),n.lanes|=1),e)}var rg=pt.ReactCurrentOwner,_e=!1;function ge(e,t,n,r){t.child=e===null?th(t,null,n,r):Vn(t,e.child,n,r)}function Wu(e,t,n,r,s){n=n.render;var i=t.ref;return zn(t,s),r=Cl(e,t,n,r,i,s),n=Pl(),e!==null&&!_e?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,ft(e,t,s)):(V&&n&&fl(t),t.flags|=1,ge(e,t,r,s),t.child)}function Hu(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!Rl(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,jh(e,t,i,r,s)):(e=Qs(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:zr,n(o,r)&&e.ref===t.ref)return ft(e,t,s)}return t.flags|=1,e=Ot(i,r),e.ref=t.ref,e.return=t,t.child=e}function jh(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(zr(i,r)&&e.ref===t.ref)if(_e=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(_e=!0);else return t.lanes=e.lanes,ft(e,t,s)}return ma(e,t,n,r,s)}function Eh(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(In,Pe),Pe|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(In,Pe),Pe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,B(In,Pe),Pe|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,B(In,Pe),Pe|=r;return ge(e,t,s,n),t.child}function Mh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ma(e,t,n,r,s){var i=Se(n)?nn:fe.current;return i=Wn(t,i),zn(t,s),n=Cl(e,t,n,r,i,s),r=Pl(),e!==null&&!_e?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,ft(e,t,s)):(V&&r&&fl(t),t.flags|=1,ge(e,t,n,s),t.child)}function Vu(e,t,n,r,s){if(Se(n)){var i=!0;ui(t)}else i=!1;if(zn(t,s),t.stateNode===null)Ks(e,t),bh(t,n,r),pa(t,n,r,s),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=Le(u):(u=Se(n)?nn:fe.current,u=Wn(t,u));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||l!==u)&&Fu(t,o,r,u),xt=!1;var f=t.memoizedState;o.state=f,pi(t,r,o,s),l=t.memoizedState,a!==r||f!==l||ke.current||xt?(typeof d=="function"&&(fa(t,n,d,r),l=t.memoizedState),(a=xt||Du(t,n,a,r,f,l,u))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,rh(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:Ue(t.type,a),o.props=u,h=t.pendingProps,f=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=Le(l):(l=Se(n)?nn:fe.current,l=Wn(t,l));var y=n.getDerivedStateFromProps;(d=typeof y=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==h||f!==l)&&Fu(t,o,r,l),xt=!1,f=t.memoizedState,o.state=f,pi(t,r,o,s);var v=t.memoizedState;a!==h||f!==v||ke.current||xt?(typeof y=="function"&&(fa(t,n,y,r),v=t.memoizedState),(u=xt||Du(t,n,u,r,f,v,l)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,v,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,v,l)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),o.props=r,o.state=v,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return ya(e,t,n,r,i,s)}function ya(e,t,n,r,s,i){Mh(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return s&&Nu(t,n,!1),ft(e,t,i);r=t.stateNode,rg.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Vn(t,e.child,null,i),t.child=Vn(t,null,a,i)):ge(e,t,a,i),t.memoizedState=r.state,s&&Nu(t,n,!0),t.child}function Nh(e){var t=e.stateNode;t.pendingContext?Mu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Mu(e,t.context,!1),_l(e,t.containerInfo)}function qu(e,t,n,r,s){return Hn(),gl(s),t.flags|=256,ge(e,t,n,r),t.child}var va={dehydrated:null,treeContext:null,retryLane:0};function wa(e){return{baseLanes:e,cachePool:null,transitions:null}}function Th(e,t,n){var r=t.pendingProps,s=K.current,i=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),B(K,s&1),e===null)return da(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=zi(o,r,0,null),e=tn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=wa(n),t.memoizedState=va,e):Ml(t,o));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return sg(e,t,o,r,a,s,n);if(i){i=r.fallback,o=t.mode,s=e.child,a=s.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Ot(s,l),r.subtreeFlags=s.subtreeFlags&14680064),a!==null?i=Ot(a,i):(i=tn(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?wa(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=va,r}return i=e.child,e=i.sibling,r=Ot(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ml(e,t){return t=zi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Cs(e,t,n,r){return r!==null&&gl(r),Vn(t,e.child,null,n),e=Ml(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function sg(e,t,n,r,s,i,o){if(n)return t.flags&256?(t.flags&=-257,r=xo(Error(C(422))),Cs(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=zi({mode:"visible",children:r.children},s,0,null),i=tn(i,s,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Vn(t,e.child,null,o),t.child.memoizedState=wa(o),t.memoizedState=va,i);if(!(t.mode&1))return Cs(e,t,o,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(C(419)),r=xo(i,r,void 0),Cs(e,t,o,r)}if(a=(o&e.childLanes)!==0,_e||a){if(r=oe,r!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|o)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,ht(e,s),qe(r,e,s,-1))}return Il(),r=xo(Error(C(421))),Cs(e,t,o,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=yg.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,je=Nt(s.nextSibling),Ee=t,V=!0,He=null,e!==null&&(Ae[$e++]=at,Ae[$e++]=lt,Ae[$e++]=rn,at=e.id,lt=e.overflow,rn=t),t=Ml(t,r.children),t.flags|=4096,t)}function Ku(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ha(e.return,t,n)}function _o(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function Ah(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(ge(e,t,r.children,n),r=K.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ku(e,n,t);else if(e.tag===19)Ku(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(B(K,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&gi(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),_o(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&gi(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}_o(t,!0,n,null,i);break;case"together":_o(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ks(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ft(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),on|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(C(153));if(t.child!==null){for(e=t.child,n=Ot(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ot(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function ig(e,t,n){switch(t.tag){case 3:Nh(t),Hn();break;case 5:sh(t);break;case 1:Se(t.type)&&ui(t);break;case 4:_l(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;B(hi,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(B(K,K.current&1),t.flags|=128,null):n&t.child.childLanes?Th(e,t,n):(B(K,K.current&1),e=ft(e,t,n),e!==null?e.sibling:null);B(K,K.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Ah(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),B(K,K.current),r)break;return null;case 22:case 23:return t.lanes=0,Eh(e,t,n)}return ft(e,t,n)}var $h,xa,Oh,Ih;$h=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};xa=function(){};Oh=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,Zt(tt.current);var i=null;switch(n){case"input":s=Bo(e,s),r=Bo(e,r),i=[];break;case"select":s=J({},s,{value:void 0}),r=J({},r,{value:void 0}),i=[];break;case"textarea":s=Vo(e,s),r=Vo(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ai)}Ko(n,r);var o;n=null;for(u in s)if(!r.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var a=s[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&($r.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=s!=null?s[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&($r.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&W("scroll",e),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Ih=function(e,t,n,r){n!==r&&(t.flags|=4)};function ur(e,t){if(!V)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function de(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function og(e,t,n){var r=t.pendingProps;switch(pl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return de(t),null;case 1:return Se(t.type)&&li(),de(t),null;case 3:return r=t.stateNode,qn(),H(ke),H(fe),Sl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ss(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,He!==null&&(Ea(He),He=null))),xa(e,t),de(t),null;case 5:kl(t);var s=Zt(Vr.current);if(n=t.type,e!==null&&t.stateNode!=null)Oh(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(C(166));return de(t),null}if(e=Zt(tt.current),Ss(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Ze]=t,r[Wr]=i,e=(t.mode&1)!==0,n){case"dialog":W("cancel",r),W("close",r);break;case"iframe":case"object":case"embed":W("load",r);break;case"video":case"audio":for(s=0;s<yr.length;s++)W(yr[s],r);break;case"source":W("error",r);break;case"img":case"image":case"link":W("error",r),W("load",r);break;case"details":W("toggle",r);break;case"input":nu(r,i),W("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},W("invalid",r);break;case"textarea":su(r,i),W("invalid",r)}Ko(n,i),s=null;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];o==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&ks(r.textContent,a,e),s=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&ks(r.textContent,a,e),s=["children",""+a]):$r.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&W("scroll",r)}switch(n){case"input":ps(r),ru(r,i,!0);break;case"textarea":ps(r),iu(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=ai)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ud(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[Ze]=t,e[Wr]=r,$h(e,t,!1,!1),t.stateNode=e;e:{switch(o=Go(n,r),n){case"dialog":W("cancel",e),W("close",e),s=r;break;case"iframe":case"object":case"embed":W("load",e),s=r;break;case"video":case"audio":for(s=0;s<yr.length;s++)W(yr[s],e);s=r;break;case"source":W("error",e),s=r;break;case"img":case"image":case"link":W("error",e),W("load",e),s=r;break;case"details":W("toggle",e),s=r;break;case"input":nu(e,r),s=Bo(e,r),W("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=J({},r,{value:void 0}),W("invalid",e);break;case"textarea":su(e,r),s=Vo(e,r),W("invalid",e);break;default:s=r}Ko(n,s),a=s;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?hd(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&cd(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Or(e,l):typeof l=="number"&&Or(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&($r.hasOwnProperty(i)?l!=null&&i==="onScroll"&&W("scroll",e):l!=null&&Za(e,i,l,o))}switch(n){case"input":ps(e),ru(e,r,!1);break;case"textarea":ps(e),iu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Rt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Rn(e,!!r.multiple,i,!1):r.defaultValue!=null&&Rn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=ai)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return de(t),null;case 6:if(e&&t.stateNode!=null)Ih(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(C(166));if(n=Zt(Vr.current),Zt(tt.current),Ss(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ze]=t,(i=r.nodeValue!==n)&&(e=Ee,e!==null))switch(e.tag){case 3:ks(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ks(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ze]=t,t.stateNode=r}return de(t),null;case 13:if(H(K),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(V&&je!==null&&t.mode&1&&!(t.flags&128))Zd(),Hn(),t.flags|=98560,i=!1;else if(i=Ss(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(C(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(C(317));i[Ze]=t}else Hn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;de(t),i=!1}else He!==null&&(Ea(He),He=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||K.current&1?se===0&&(se=3):Il())),t.updateQueue!==null&&(t.flags|=4),de(t),null);case 4:return qn(),xa(e,t),e===null&&Ur(t.stateNode.containerInfo),de(t),null;case 10:return vl(t.type._context),de(t),null;case 17:return Se(t.type)&&li(),de(t),null;case 19:if(H(K),i=t.memoizedState,i===null)return de(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)ur(i,!1);else{if(se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=gi(e),o!==null){for(t.flags|=128,ur(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return B(K,K.current&1|2),t.child}e=e.sibling}i.tail!==null&&Y()>Gn&&(t.flags|=128,r=!0,ur(i,!1),t.lanes=4194304)}else{if(!r)if(e=gi(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ur(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!V)return de(t),null}else 2*Y()-i.renderingStartTime>Gn&&n!==1073741824&&(t.flags|=128,r=!0,ur(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Y(),t.sibling=null,n=K.current,B(K,r?n&1|2:n&1),t):(de(t),null);case 22:case 23:return Ol(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Pe&1073741824&&(de(t),t.subtreeFlags&6&&(t.flags|=8192)):de(t),null;case 24:return null;case 25:return null}throw Error(C(156,t.tag))}function ag(e,t){switch(pl(t),t.tag){case 1:return Se(t.type)&&li(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return qn(),H(ke),H(fe),Sl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return kl(t),null;case 13:if(H(K),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(C(340));Hn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return H(K),null;case 4:return qn(),null;case 10:return vl(t.type._context),null;case 22:case 23:return Ol(),null;case 24:return null;default:return null}}var Ps=!1,he=!1,lg=typeof WeakSet=="function"?WeakSet:Set,T=null;function On(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Q(e,t,r)}else n.current=null}function _a(e,t,n){try{n()}catch(r){Q(e,t,r)}}var Gu=!1;function ug(e,t){if(sa=si,e=zd(),hl(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,d=0,h=e,f=null;t:for(;;){for(var y;h!==n||s!==0&&h.nodeType!==3||(a=o+s),h!==i||r!==0&&h.nodeType!==3||(l=o+r),h.nodeType===3&&(o+=h.nodeValue.length),(y=h.firstChild)!==null;)f=h,h=y;for(;;){if(h===e)break t;if(f===n&&++u===s&&(a=o),f===i&&++d===r&&(l=o),(y=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=y}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(ia={focusedElem:e,selectionRange:n},si=!1,T=t;T!==null;)if(t=T,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,T=e;else for(;T!==null;){t=T;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,_=v.memoizedState,m=t.stateNode,p=m.getSnapshotBeforeUpdate(t.elementType===t.type?x:Ue(t.type,x),_);m.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(C(163))}}catch(w){Q(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,T=e;break}T=t.return}return v=Gu,Gu=!1,v}function jr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&_a(t,n,i)}s=s.next}while(s!==r)}}function Di(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ka(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Rh(e){var t=e.alternate;t!==null&&(e.alternate=null,Rh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ze],delete t[Wr],delete t[la],delete t[V0],delete t[q0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Lh(e){return e.tag===5||e.tag===3||e.tag===4}function Ju(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Lh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Sa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ai));else if(r!==4&&(e=e.child,e!==null))for(Sa(e,t,n),e=e.sibling;e!==null;)Sa(e,t,n),e=e.sibling}function ba(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ba(e,t,n),e=e.sibling;e!==null;)ba(e,t,n),e=e.sibling}var ae=null,Be=!1;function mt(e,t,n){for(n=n.child;n!==null;)Dh(e,t,n),n=n.sibling}function Dh(e,t,n){if(et&&typeof et.onCommitFiberUnmount=="function")try{et.onCommitFiberUnmount(Ni,n)}catch{}switch(n.tag){case 5:he||On(n,t);case 6:var r=ae,s=Be;ae=null,mt(e,t,n),ae=r,Be=s,ae!==null&&(Be?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(Be?(e=ae,n=n.stateNode,e.nodeType===8?po(e.parentNode,n):e.nodeType===1&&po(e,n),Dr(e)):po(ae,n.stateNode));break;case 4:r=ae,s=Be,ae=n.stateNode.containerInfo,Be=!0,mt(e,t,n),ae=r,Be=s;break;case 0:case 11:case 14:case 15:if(!he&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&_a(n,t,o),s=s.next}while(s!==r)}mt(e,t,n);break;case 1:if(!he&&(On(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){Q(n,t,a)}mt(e,t,n);break;case 21:mt(e,t,n);break;case 22:n.mode&1?(he=(r=he)||n.memoizedState!==null,mt(e,t,n),he=r):mt(e,t,n);break;default:mt(e,t,n)}}function Qu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new lg),t.forEach(function(r){var s=vg.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function Fe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:ae=a.stateNode,Be=!1;break e;case 3:ae=a.stateNode.containerInfo,Be=!0;break e;case 4:ae=a.stateNode.containerInfo,Be=!0;break e}a=a.return}if(ae===null)throw Error(C(160));Dh(i,o,s),ae=null,Be=!1;var l=s.alternate;l!==null&&(l.return=null),s.return=null}catch(u){Q(s,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Fh(t,e),t=t.sibling}function Fh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Fe(t,e),Qe(e),r&4){try{jr(3,e,e.return),Di(3,e)}catch(x){Q(e,e.return,x)}try{jr(5,e,e.return)}catch(x){Q(e,e.return,x)}}break;case 1:Fe(t,e),Qe(e),r&512&&n!==null&&On(n,n.return);break;case 5:if(Fe(t,e),Qe(e),r&512&&n!==null&&On(n,n.return),e.flags&32){var s=e.stateNode;try{Or(s,"")}catch(x){Q(e,e.return,x)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&ad(s,i),Go(a,o);var u=Go(a,i);for(o=0;o<l.length;o+=2){var d=l[o],h=l[o+1];d==="style"?hd(s,h):d==="dangerouslySetInnerHTML"?cd(s,h):d==="children"?Or(s,h):Za(s,d,h,u)}switch(a){case"input":Wo(s,i);break;case"textarea":ld(s,i);break;case"select":var f=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var y=i.value;y!=null?Rn(s,!!i.multiple,y,!1):f!==!!i.multiple&&(i.defaultValue!=null?Rn(s,!!i.multiple,i.defaultValue,!0):Rn(s,!!i.multiple,i.multiple?[]:"",!1))}s[Wr]=i}catch(x){Q(e,e.return,x)}}break;case 6:if(Fe(t,e),Qe(e),r&4){if(e.stateNode===null)throw Error(C(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(x){Q(e,e.return,x)}}break;case 3:if(Fe(t,e),Qe(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Dr(t.containerInfo)}catch(x){Q(e,e.return,x)}break;case 4:Fe(t,e),Qe(e);break;case 13:Fe(t,e),Qe(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(Al=Y())),r&4&&Qu(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(he=(u=he)||d,Fe(t,e),he=u):Fe(t,e),Qe(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(T=e,d=e.child;d!==null;){for(h=T=d;T!==null;){switch(f=T,y=f.child,f.tag){case 0:case 11:case 14:case 15:jr(4,f,f.return);break;case 1:On(f,f.return);var v=f.stateNode;if(typeof v.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){Q(r,n,x)}}break;case 5:On(f,f.return);break;case 22:if(f.memoizedState!==null){Yu(h);continue}}y!==null?(y.return=f,T=y):Yu(h)}d=d.sibling}e:for(d=null,h=e;;){if(h.tag===5){if(d===null){d=h;try{s=h.stateNode,u?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=h.stateNode,l=h.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=dd("display",o))}catch(x){Q(e,e.return,x)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(x){Q(e,e.return,x)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:Fe(t,e),Qe(e),r&4&&Qu(e);break;case 21:break;default:Fe(t,e),Qe(e)}}function Qe(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Lh(n)){var r=n;break e}n=n.return}throw Error(C(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(Or(s,""),r.flags&=-33);var i=Ju(e);ba(e,i,s);break;case 3:case 4:var o=r.stateNode.containerInfo,a=Ju(e);Sa(e,a,o);break;default:throw Error(C(161))}}catch(l){Q(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function cg(e,t,n){T=e,zh(e)}function zh(e,t,n){for(var r=(e.mode&1)!==0;T!==null;){var s=T,i=s.child;if(s.tag===22&&r){var o=s.memoizedState!==null||Ps;if(!o){var a=s.alternate,l=a!==null&&a.memoizedState!==null||he;a=Ps;var u=he;if(Ps=o,(he=l)&&!u)for(T=s;T!==null;)o=T,l=o.child,o.tag===22&&o.memoizedState!==null?Zu(s):l!==null?(l.return=o,T=l):Zu(s);for(;i!==null;)T=i,zh(i),i=i.sibling;T=s,Ps=a,he=u}Xu(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,T=i):Xu(e)}}function Xu(e){for(;T!==null;){var t=T;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:he||Di(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!he)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:Ue(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Iu(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Iu(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&Dr(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(C(163))}he||t.flags&512&&ka(t)}catch(f){Q(t,t.return,f)}}if(t===e){T=null;break}if(n=t.sibling,n!==null){n.return=t.return,T=n;break}T=t.return}}function Yu(e){for(;T!==null;){var t=T;if(t===e){T=null;break}var n=t.sibling;if(n!==null){n.return=t.return,T=n;break}T=t.return}}function Zu(e){for(;T!==null;){var t=T;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Di(4,t)}catch(l){Q(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(l){Q(t,s,l)}}var i=t.return;try{ka(t)}catch(l){Q(t,i,l)}break;case 5:var o=t.return;try{ka(t)}catch(l){Q(t,o,l)}}}catch(l){Q(t,t.return,l)}if(t===e){T=null;break}var a=t.sibling;if(a!==null){a.return=t.return,T=a;break}T=t.return}}var dg=Math.ceil,vi=pt.ReactCurrentDispatcher,Nl=pt.ReactCurrentOwner,Ie=pt.ReactCurrentBatchConfig,D=0,oe=null,Z=null,le=0,Pe=0,In=Ft(0),se=0,Jr=null,on=0,Fi=0,Tl=0,Er=null,we=null,Al=0,Gn=1/0,st=null,wi=!1,Ca=null,At=null,js=!1,Ct=null,xi=0,Mr=0,Pa=null,Gs=-1,Js=0;function me(){return D&6?Y():Gs!==-1?Gs:Gs=Y()}function $t(e){return e.mode&1?D&2&&le!==0?le&-le:G0.transition!==null?(Js===0&&(Js=bd()),Js):(e=z,e!==0||(e=window.event,e=e===void 0?16:Td(e.type)),e):1}function qe(e,t,n,r){if(50<Mr)throw Mr=0,Pa=null,Error(C(185));es(e,n,r),(!(D&2)||e!==oe)&&(e===oe&&(!(D&2)&&(Fi|=n),se===4&&kt(e,le)),be(e,r),n===1&&D===0&&!(t.mode&1)&&(Gn=Y()+500,Ii&&zt()))}function be(e,t){var n=e.callbackNode;Gp(e,t);var r=ri(e,e===oe?le:0);if(r===0)n!==null&&lu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&lu(n),t===1)e.tag===0?K0(ec.bind(null,e)):Qd(ec.bind(null,e)),W0(function(){!(D&6)&&zt()}),n=null;else{switch(Cd(r)){case 1:n=sl;break;case 4:n=kd;break;case 16:n=ni;break;case 536870912:n=Sd;break;default:n=ni}n=Gh(n,Uh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Uh(e,t){if(Gs=-1,Js=0,D&6)throw Error(C(327));var n=e.callbackNode;if(Un()&&e.callbackNode!==n)return null;var r=ri(e,e===oe?le:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=_i(e,r);else{t=r;var s=D;D|=2;var i=Wh();(oe!==e||le!==t)&&(st=null,Gn=Y()+500,en(e,t));do try{pg();break}catch(a){Bh(e,a)}while(!0);yl(),vi.current=i,D=s,Z!==null?t=0:(oe=null,le=0,t=se)}if(t!==0){if(t===2&&(s=Zo(e),s!==0&&(r=s,t=ja(e,s))),t===1)throw n=Jr,en(e,0),kt(e,r),be(e,Y()),n;if(t===6)kt(e,r);else{if(s=e.current.alternate,!(r&30)&&!hg(s)&&(t=_i(e,r),t===2&&(i=Zo(e),i!==0&&(r=i,t=ja(e,i))),t===1))throw n=Jr,en(e,0),kt(e,r),be(e,Y()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(C(345));case 2:Gt(e,we,st);break;case 3:if(kt(e,r),(r&130023424)===r&&(t=Al+500-Y(),10<t)){if(ri(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){me(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=aa(Gt.bind(null,e,we,st),t);break}Gt(e,we,st);break;case 4:if(kt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var o=31-Ve(r);i=1<<o,o=t[o],o>s&&(s=o),r&=~i}if(r=s,r=Y()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*dg(r/1960))-r,10<r){e.timeoutHandle=aa(Gt.bind(null,e,we,st),r);break}Gt(e,we,st);break;case 5:Gt(e,we,st);break;default:throw Error(C(329))}}}return be(e,Y()),e.callbackNode===n?Uh.bind(null,e):null}function ja(e,t){var n=Er;return e.current.memoizedState.isDehydrated&&(en(e,t).flags|=256),e=_i(e,t),e!==2&&(t=we,we=n,t!==null&&Ea(t)),e}function Ea(e){we===null?we=e:we.push.apply(we,e)}function hg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!Ke(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function kt(e,t){for(t&=~Tl,t&=~Fi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ve(t),r=1<<n;e[n]=-1,t&=~r}}function ec(e){if(D&6)throw Error(C(327));Un();var t=ri(e,0);if(!(t&1))return be(e,Y()),null;var n=_i(e,t);if(e.tag!==0&&n===2){var r=Zo(e);r!==0&&(t=r,n=ja(e,r))}if(n===1)throw n=Jr,en(e,0),kt(e,t),be(e,Y()),n;if(n===6)throw Error(C(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Gt(e,we,st),be(e,Y()),null}function $l(e,t){var n=D;D|=1;try{return e(t)}finally{D=n,D===0&&(Gn=Y()+500,Ii&&zt())}}function an(e){Ct!==null&&Ct.tag===0&&!(D&6)&&Un();var t=D;D|=1;var n=Ie.transition,r=z;try{if(Ie.transition=null,z=1,e)return e()}finally{z=r,Ie.transition=n,D=t,!(D&6)&&zt()}}function Ol(){Pe=In.current,H(In)}function en(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,B0(n)),Z!==null)for(n=Z.return;n!==null;){var r=n;switch(pl(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&li();break;case 3:qn(),H(ke),H(fe),Sl();break;case 5:kl(r);break;case 4:qn();break;case 13:H(K);break;case 19:H(K);break;case 10:vl(r.type._context);break;case 22:case 23:Ol()}n=n.return}if(oe=e,Z=e=Ot(e.current,null),le=Pe=t,se=0,Jr=null,Tl=Fi=on=0,we=Er=null,Yt!==null){for(t=0;t<Yt.length;t++)if(n=Yt[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=s,r.next=o}n.pending=r}Yt=null}return e}function Bh(e,t){do{var n=Z;try{if(yl(),Vs.current=yi,mi){for(var r=G.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}mi=!1}if(sn=0,ie=re=G=null,Pr=!1,qr=0,Nl.current=null,n===null||n.return===null){se=1,Jr=t,Z=null;break}e:{var i=e,o=n.return,a=n,l=t;if(t=le,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,d=a,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var y=Uu(o);if(y!==null){y.flags&=-257,Bu(y,o,a,i,t),y.mode&1&&zu(i,u,t),t=y,l=u;var v=t.updateQueue;if(v===null){var x=new Set;x.add(l),t.updateQueue=x}else v.add(l);break e}else{if(!(t&1)){zu(i,u,t),Il();break e}l=Error(C(426))}}else if(V&&a.mode&1){var _=Uu(o);if(_!==null){!(_.flags&65536)&&(_.flags|=256),Bu(_,o,a,i,t),gl(Kn(l,a));break e}}i=l=Kn(l,a),se!==4&&(se=2),Er===null?Er=[i]:Er.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=Ch(i,l,t);Ou(i,m);break e;case 1:a=l;var p=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(At===null||!At.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var w=Ph(i,a,t);Ou(i,w);break e}}i=i.return}while(i!==null)}Vh(n)}catch(S){t=S,Z===n&&n!==null&&(Z=n=n.return);continue}break}while(!0)}function Wh(){var e=vi.current;return vi.current=yi,e===null?yi:e}function Il(){(se===0||se===3||se===2)&&(se=4),oe===null||!(on&268435455)&&!(Fi&268435455)||kt(oe,le)}function _i(e,t){var n=D;D|=2;var r=Wh();(oe!==e||le!==t)&&(st=null,en(e,t));do try{fg();break}catch(s){Bh(e,s)}while(!0);if(yl(),D=n,vi.current=r,Z!==null)throw Error(C(261));return oe=null,le=0,se}function fg(){for(;Z!==null;)Hh(Z)}function pg(){for(;Z!==null&&!Fp();)Hh(Z)}function Hh(e){var t=Kh(e.alternate,e,Pe);e.memoizedProps=e.pendingProps,t===null?Vh(e):Z=t,Nl.current=null}function Vh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=ag(n,t),n!==null){n.flags&=32767,Z=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{se=6,Z=null;return}}else if(n=og(n,t,Pe),n!==null){Z=n;return}if(t=t.sibling,t!==null){Z=t;return}Z=t=e}while(t!==null);se===0&&(se=5)}function Gt(e,t,n){var r=z,s=Ie.transition;try{Ie.transition=null,z=1,gg(e,t,n,r)}finally{Ie.transition=s,z=r}return null}function gg(e,t,n,r){do Un();while(Ct!==null);if(D&6)throw Error(C(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(C(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Jp(e,i),e===oe&&(Z=oe=null,le=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||js||(js=!0,Gh(ni,function(){return Un(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ie.transition,Ie.transition=null;var o=z;z=1;var a=D;D|=4,Nl.current=null,ug(e,n),Fh(n,e),I0(ia),si=!!sa,ia=sa=null,e.current=n,cg(n),zp(),D=a,z=o,Ie.transition=i}else e.current=n;if(js&&(js=!1,Ct=e,xi=s),i=e.pendingLanes,i===0&&(At=null),Wp(n.stateNode),be(e,Y()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(wi)throw wi=!1,e=Ca,Ca=null,e;return xi&1&&e.tag!==0&&Un(),i=e.pendingLanes,i&1?e===Pa?Mr++:(Mr=0,Pa=e):Mr=0,zt(),null}function Un(){if(Ct!==null){var e=Cd(xi),t=Ie.transition,n=z;try{if(Ie.transition=null,z=16>e?16:e,Ct===null)var r=!1;else{if(e=Ct,Ct=null,xi=0,D&6)throw Error(C(331));var s=D;for(D|=4,T=e.current;T!==null;){var i=T,o=i.child;if(T.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(T=u;T!==null;){var d=T;switch(d.tag){case 0:case 11:case 15:jr(8,d,i)}var h=d.child;if(h!==null)h.return=d,T=h;else for(;T!==null;){d=T;var f=d.sibling,y=d.return;if(Rh(d),d===u){T=null;break}if(f!==null){f.return=y,T=f;break}T=y}}}var v=i.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var _=x.sibling;x.sibling=null,x=_}while(x!==null)}}T=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,T=o;else e:for(;T!==null;){if(i=T,i.flags&2048)switch(i.tag){case 0:case 11:case 15:jr(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,T=m;break e}T=i.return}}var p=e.current;for(T=p;T!==null;){o=T;var g=o.child;if(o.subtreeFlags&2064&&g!==null)g.return=o,T=g;else e:for(o=p;T!==null;){if(a=T,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Di(9,a)}}catch(S){Q(a,a.return,S)}if(a===o){T=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,T=w;break e}T=a.return}}if(D=s,zt(),et&&typeof et.onPostCommitFiberRoot=="function")try{et.onPostCommitFiberRoot(Ni,e)}catch{}r=!0}return r}finally{z=n,Ie.transition=t}}return!1}function tc(e,t,n){t=Kn(n,t),t=Ch(e,t,1),e=Tt(e,t,1),t=me(),e!==null&&(es(e,1,t),be(e,t))}function Q(e,t,n){if(e.tag===3)tc(e,e,n);else for(;t!==null;){if(t.tag===3){tc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(At===null||!At.has(r))){e=Kn(n,e),e=Ph(t,e,1),t=Tt(t,e,1),e=me(),t!==null&&(es(t,1,e),be(t,e));break}}t=t.return}}function mg(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=me(),e.pingedLanes|=e.suspendedLanes&n,oe===e&&(le&n)===n&&(se===4||se===3&&(le&130023424)===le&&500>Y()-Al?en(e,0):Tl|=n),be(e,t)}function qh(e,t){t===0&&(e.mode&1?(t=ys,ys<<=1,!(ys&130023424)&&(ys=4194304)):t=1);var n=me();e=ht(e,t),e!==null&&(es(e,t,n),be(e,n))}function yg(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),qh(e,n)}function vg(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(C(314))}r!==null&&r.delete(t),qh(e,n)}var Kh;Kh=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ke.current)_e=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return _e=!1,ig(e,t,n);_e=!!(e.flags&131072)}else _e=!1,V&&t.flags&1048576&&Xd(t,di,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ks(e,t),e=t.pendingProps;var s=Wn(t,fe.current);zn(t,n),s=Cl(null,t,r,e,s,n);var i=Pl();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Se(r)?(i=!0,ui(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,xl(t),s.updater=Li,t.stateNode=s,s._reactInternals=t,pa(t,r,e,n),t=ya(null,t,r,!0,i,n)):(t.tag=0,V&&i&&fl(t),ge(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ks(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=xg(r),e=Ue(r,e),s){case 0:t=ma(null,t,r,e,n);break e;case 1:t=Vu(null,t,r,e,n);break e;case 11:t=Wu(null,t,r,e,n);break e;case 14:t=Hu(null,t,r,Ue(r.type,e),n);break e}throw Error(C(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Ue(r,s),ma(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Ue(r,s),Vu(e,t,r,s,n);case 3:e:{if(Nh(t),e===null)throw Error(C(387));r=t.pendingProps,i=t.memoizedState,s=i.element,rh(e,t),pi(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=Kn(Error(C(423)),t),t=qu(e,t,r,n,s);break e}else if(r!==s){s=Kn(Error(C(424)),t),t=qu(e,t,r,n,s);break e}else for(je=Nt(t.stateNode.containerInfo.firstChild),Ee=t,V=!0,He=null,n=th(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Hn(),r===s){t=ft(e,t,n);break e}ge(e,t,r,n)}t=t.child}return t;case 5:return sh(t),e===null&&da(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,o=s.children,oa(r,s)?o=null:i!==null&&oa(r,i)&&(t.flags|=32),Mh(e,t),ge(e,t,o,n),t.child;case 6:return e===null&&da(t),null;case 13:return Th(e,t,n);case 4:return _l(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Vn(t,null,r,n):ge(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Ue(r,s),Wu(e,t,r,s,n);case 7:return ge(e,t,t.pendingProps,n),t.child;case 8:return ge(e,t,t.pendingProps.children,n),t.child;case 12:return ge(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,o=s.value,B(hi,r._currentValue),r._currentValue=o,i!==null)if(Ke(i.value,o)){if(i.children===s.children&&!ke.current){t=ft(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){o=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=ut(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?l.next=l:(l.next=d.next,d.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),ha(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(C(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),ha(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}ge(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,zn(t,n),s=Le(s),r=r(s),t.flags|=1,ge(e,t,r,n),t.child;case 14:return r=t.type,s=Ue(r,t.pendingProps),s=Ue(r.type,s),Hu(e,t,r,s,n);case 15:return jh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Ue(r,s),Ks(e,t),t.tag=1,Se(r)?(e=!0,ui(t)):e=!1,zn(t,n),bh(t,r,s),pa(t,r,s,n),ya(null,t,r,!0,e,n);case 19:return Ah(e,t,n);case 22:return Eh(e,t,n)}throw Error(C(156,t.tag))};function Gh(e,t){return _d(e,t)}function wg(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Oe(e,t,n,r){return new wg(e,t,n,r)}function Rl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function xg(e){if(typeof e=="function")return Rl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===tl)return 11;if(e===nl)return 14}return 2}function Ot(e,t){var n=e.alternate;return n===null?(n=Oe(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Qs(e,t,n,r,s,i){var o=2;if(r=e,typeof e=="function")Rl(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Cn:return tn(n.children,s,i,t);case el:o=8,s|=8;break;case Do:return e=Oe(12,n,t,s|2),e.elementType=Do,e.lanes=i,e;case Fo:return e=Oe(13,n,t,s),e.elementType=Fo,e.lanes=i,e;case zo:return e=Oe(19,n,t,s),e.elementType=zo,e.lanes=i,e;case sd:return zi(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case nd:o=10;break e;case rd:o=9;break e;case tl:o=11;break e;case nl:o=14;break e;case wt:o=16,r=null;break e}throw Error(C(130,e==null?e:typeof e,""))}return t=Oe(o,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function tn(e,t,n,r){return e=Oe(7,e,r,t),e.lanes=n,e}function zi(e,t,n,r){return e=Oe(22,e,r,t),e.elementType=sd,e.lanes=n,e.stateNode={isHidden:!1},e}function ko(e,t,n){return e=Oe(6,e,null,t),e.lanes=n,e}function So(e,t,n){return t=Oe(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function _g(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=no(0),this.expirationTimes=no(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=no(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Ll(e,t,n,r,s,i,o,a,l){return e=new _g(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Oe(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},xl(i),e}function kg(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:bn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Jh(e){if(!e)return Lt;e=e._reactInternals;e:{if(un(e)!==e||e.tag!==1)throw Error(C(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Se(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(C(171))}if(e.tag===1){var n=e.type;if(Se(n))return Jd(e,n,t)}return t}function Qh(e,t,n,r,s,i,o,a,l){return e=Ll(n,r,!0,e,s,i,o,a,l),e.context=Jh(null),n=e.current,r=me(),s=$t(n),i=ut(r,s),i.callback=t??null,Tt(n,i,s),e.current.lanes=s,es(e,s,r),be(e,r),e}function Ui(e,t,n,r){var s=t.current,i=me(),o=$t(s);return n=Jh(n),t.context===null?t.context=n:t.pendingContext=n,t=ut(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Tt(s,t,o),e!==null&&(qe(e,s,o,i),Hs(e,s,o)),o}function ki(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function nc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Dl(e,t){nc(e,t),(e=e.alternate)&&nc(e,t)}function Sg(){return null}var Xh=typeof reportError=="function"?reportError:function(e){console.error(e)};function Fl(e){this._internalRoot=e}Bi.prototype.render=Fl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(C(409));Ui(e,t,null,null)};Bi.prototype.unmount=Fl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;an(function(){Ui(null,e,null,null)}),t[dt]=null}};function Bi(e){this._internalRoot=e}Bi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ed();e={blockedOn:null,target:e,priority:t};for(var n=0;n<_t.length&&t!==0&&t<_t[n].priority;n++);_t.splice(n,0,e),n===0&&Nd(e)}};function zl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Wi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function rc(){}function bg(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var u=ki(o);i.call(u)}}var o=Qh(t,r,e,0,null,!1,!1,"",rc);return e._reactRootContainer=o,e[dt]=o.current,Ur(e.nodeType===8?e.parentNode:e),an(),o}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var a=r;r=function(){var u=ki(l);a.call(u)}}var l=Ll(e,0,!1,null,null,!1,!1,"",rc);return e._reactRootContainer=l,e[dt]=l.current,Ur(e.nodeType===8?e.parentNode:e),an(function(){Ui(t,l,n,r)}),l}function Hi(e,t,n,r,s){var i=n._reactRootContainer;if(i){var o=i;if(typeof s=="function"){var a=s;s=function(){var l=ki(o);a.call(l)}}Ui(t,o,e,s)}else o=bg(n,t,e,s,r);return ki(o)}Pd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=mr(t.pendingLanes);n!==0&&(il(t,n|1),be(t,Y()),!(D&6)&&(Gn=Y()+500,zt()))}break;case 13:an(function(){var r=ht(e,1);if(r!==null){var s=me();qe(r,e,1,s)}}),Dl(e,1)}};ol=function(e){if(e.tag===13){var t=ht(e,134217728);if(t!==null){var n=me();qe(t,e,134217728,n)}Dl(e,134217728)}};jd=function(e){if(e.tag===13){var t=$t(e),n=ht(e,t);if(n!==null){var r=me();qe(n,e,t,r)}Dl(e,t)}};Ed=function(){return z};Md=function(e,t){var n=z;try{return z=e,t()}finally{z=n}};Qo=function(e,t,n){switch(t){case"input":if(Wo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=Oi(r);if(!s)throw Error(C(90));od(r),Wo(r,s)}}}break;case"textarea":ld(e,n);break;case"select":t=n.value,t!=null&&Rn(e,!!n.multiple,t,!1)}};gd=$l;md=an;var Cg={usingClientEntryPoint:!1,Events:[ns,Mn,Oi,fd,pd,$l]},cr={findFiberByHostInstance:Xt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Pg={bundleType:cr.bundleType,version:cr.version,rendererPackageName:cr.rendererPackageName,rendererConfig:cr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:pt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=wd(e),e===null?null:e.stateNode},findFiberByHostInstance:cr.findFiberByHostInstance||Sg,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Es=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Es.isDisabled&&Es.supportsFiber)try{Ni=Es.inject(Pg),et=Es}catch{}}Ne.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Cg;Ne.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!zl(t))throw Error(C(200));return kg(e,t,null,n)};Ne.createRoot=function(e,t){if(!zl(e))throw Error(C(299));var n=!1,r="",s=Xh;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=Ll(e,1,!1,null,null,n,!1,r,s),e[dt]=t.current,Ur(e.nodeType===8?e.parentNode:e),new Fl(t)};Ne.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(C(188)):(e=Object.keys(e).join(","),Error(C(268,e)));return e=wd(t),e=e===null?null:e.stateNode,e};Ne.flushSync=function(e){return an(e)};Ne.hydrate=function(e,t,n){if(!Wi(t))throw Error(C(200));return Hi(null,e,t,!0,n)};Ne.hydrateRoot=function(e,t,n){if(!zl(e))throw Error(C(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",o=Xh;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Qh(t,null,e,1,n??null,s,!1,i,o),e[dt]=t.current,Ur(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new Bi(t)};Ne.render=function(e,t,n){if(!Wi(t))throw Error(C(200));return Hi(null,e,t,!1,n)};Ne.unmountComponentAtNode=function(e){if(!Wi(e))throw Error(C(40));return e._reactRootContainer?(an(function(){Hi(null,null,e,!1,function(){e._reactRootContainer=null,e[dt]=null})}),!0):!1};Ne.unstable_batchedUpdates=$l;Ne.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Wi(n))throw Error(C(200));if(e==null||e._reactInternals===void 0)throw Error(C(38));return Hi(e,t,n,!1,r)};Ne.version="18.3.1-next-f1338f8080-20240426";function Yh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Yh)}catch(e){console.error(e)}}Yh(),Yc.exports=Ne;var jg=Yc.exports,sc=jg;Ro.createRoot=sc.createRoot,Ro.hydrateRoot=sc.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Qr(){return Qr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Qr.apply(this,arguments)}var Pt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Pt||(Pt={}));const ic="popstate";function Eg(e){e===void 0&&(e={});function t(r,s){let{pathname:i,search:o,hash:a}=r.location;return Ma("",{pathname:i,search:o,hash:a},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function n(r,s){return typeof s=="string"?s:Si(s)}return Ng(t,n,null,e)}function ee(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Zh(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Mg(){return Math.random().toString(36).substr(2,8)}function oc(e,t){return{usr:e.state,key:e.key,idx:t}}function Ma(e,t,n,r){return n===void 0&&(n=null),Qr({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Zn(t):t,{state:n,key:t&&t.key||r||Mg()})}function Si(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Zn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Ng(e,t,n,r){r===void 0&&(r={});let{window:s=document.defaultView,v5Compat:i=!1}=r,o=s.history,a=Pt.Pop,l=null,u=d();u==null&&(u=0,o.replaceState(Qr({},o.state,{idx:u}),""));function d(){return(o.state||{idx:null}).idx}function h(){a=Pt.Pop;let _=d(),m=_==null?null:_-u;u=_,l&&l({action:a,location:x.location,delta:m})}function f(_,m){a=Pt.Push;let p=Ma(x.location,_,m);u=d()+1;let g=oc(p,u),w=x.createHref(p);try{o.pushState(g,"",w)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;s.location.assign(w)}i&&l&&l({action:a,location:x.location,delta:1})}function y(_,m){a=Pt.Replace;let p=Ma(x.location,_,m);u=d();let g=oc(p,u),w=x.createHref(p);o.replaceState(g,"",w),i&&l&&l({action:a,location:x.location,delta:0})}function v(_){let m=s.location.origin!=="null"?s.location.origin:s.location.href,p=typeof _=="string"?_:Si(_);return p=p.replace(/ $/,"%20"),ee(m,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,m)}let x={get action(){return a},get location(){return e(s,o)},listen(_){if(l)throw new Error("A history only accepts one active listener");return s.addEventListener(ic,h),l=_,()=>{s.removeEventListener(ic,h),l=null}},createHref(_){return t(s,_)},createURL:v,encodeLocation(_){let m=v(_);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:f,replace:y,go(_){return o.go(_)}};return x}var ac;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(ac||(ac={}));function Tg(e,t,n){return n===void 0&&(n="/"),Ag(e,t,n)}function Ag(e,t,n,r){let s=typeof t=="string"?Zn(t):t,i=Ul(s.pathname||"/",n);if(i==null)return null;let o=ef(e);$g(o);let a=null;for(let l=0;a==null&&l<o.length;++l){let u=Vg(i);a=Bg(o[l],u)}return a}function ef(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let s=(i,o,a)=>{let l={relativePath:a===void 0?i.path||"":a,caseSensitive:i.caseSensitive===!0,childrenIndex:o,route:i};l.relativePath.startsWith("/")&&(ee(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=It([r,l.relativePath]),d=n.concat(l);i.children&&i.children.length>0&&(ee(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),ef(i.children,t,d,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:zg(u,i.index),routesMeta:d})};return e.forEach((i,o)=>{var a;if(i.path===""||!((a=i.path)!=null&&a.includes("?")))s(i,o);else for(let l of tf(i.path))s(i,o,l)}),t}function tf(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,s=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return s?[i,""]:[i];let o=tf(r.join("/")),a=[];return a.push(...o.map(l=>l===""?i:[i,l].join("/"))),s&&a.push(...o),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function $g(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Ug(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Og=/^:[\w-]+$/,Ig=3,Rg=2,Lg=1,Dg=10,Fg=-2,lc=e=>e==="*";function zg(e,t){let n=e.split("/"),r=n.length;return n.some(lc)&&(r+=Fg),t&&(r+=Rg),n.filter(s=>!lc(s)).reduce((s,i)=>s+(Og.test(i)?Ig:i===""?Lg:Dg),r)}function Ug(e,t){return e.length===t.length&&e.slice(0,-1).every((r,s)=>r===t[s])?e[e.length-1]-t[t.length-1]:0}function Bg(e,t,n){let{routesMeta:r}=e,s={},i="/",o=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,d=i==="/"?t:t.slice(i.length)||"/",h=Wg({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},d),f=l.route;if(!h)return null;Object.assign(s,h.params),o.push({params:s,pathname:It([i,h.pathname]),pathnameBase:Jg(It([i,h.pathnameBase])),route:f}),h.pathnameBase!=="/"&&(i=It([i,h.pathnameBase]))}return o}function Wg(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Hg(e.path,e.caseSensitive,e.end),s=t.match(n);if(!s)return null;let i=s[0],o=i.replace(/(.)\/+$/,"$1"),a=s.slice(1);return{params:r.reduce((u,d,h)=>{let{paramName:f,isOptional:y}=d;if(f==="*"){let x=a[h]||"";o=i.slice(0,i.length-x.length).replace(/(.)\/+$/,"$1")}const v=a[h];return y&&!v?u[f]=void 0:u[f]=(v||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:o,pattern:e}}function Hg(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Zh(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],s="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),s+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?s+="\\/*$":e!==""&&e!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,t?void 0:"i"),r]}function Vg(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Zh(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Ul(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function qg(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:s=""}=typeof e=="string"?Zn(e):e;return{pathname:n?n.startsWith("/")?n:Kg(n,t):t,search:Qg(r),hash:Xg(s)}}function Kg(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(s=>{s===".."?n.length>1&&n.pop():s!=="."&&n.push(s)}),n.length>1?n.join("/"):"/"}function bo(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Gg(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function nf(e,t){let n=Gg(e);return t?n.map((r,s)=>s===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function rf(e,t,n,r){r===void 0&&(r=!1);let s;typeof e=="string"?s=Zn(e):(s=Qr({},e),ee(!s.pathname||!s.pathname.includes("?"),bo("?","pathname","search",s)),ee(!s.pathname||!s.pathname.includes("#"),bo("#","pathname","hash",s)),ee(!s.search||!s.search.includes("#"),bo("#","search","hash",s)));let i=e===""||s.pathname==="",o=i?"/":s.pathname,a;if(o==null)a=n;else{let h=t.length-1;if(!r&&o.startsWith("..")){let f=o.split("/");for(;f[0]==="..";)f.shift(),h-=1;s.pathname=f.join("/")}a=h>=0?t[h]:"/"}let l=qg(s,a),u=o&&o!=="/"&&o.endsWith("/"),d=(i||o===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||d)&&(l.pathname+="/"),l}const It=e=>e.join("/").replace(/\/\/+/g,"/"),Jg=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Qg=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Xg=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Yg(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const sf=["post","put","patch","delete"];new Set(sf);const Zg=["get",...sf];new Set(Zg);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Xr(){return Xr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Xr.apply(this,arguments)}const Bl=k.createContext(null),em=k.createContext(null),cn=k.createContext(null),Vi=k.createContext(null),dn=k.createContext({outlet:null,matches:[],isDataRoute:!1}),of=k.createContext(null);function tm(e,t){let{relative:n}=t===void 0?{}:t;ss()||ee(!1);let{basename:r,navigator:s}=k.useContext(cn),{hash:i,pathname:o,search:a}=lf(e,{relative:n}),l=o;return r!=="/"&&(l=o==="/"?r:It([r,o])),s.createHref({pathname:l,search:a,hash:i})}function ss(){return k.useContext(Vi)!=null}function is(){return ss()||ee(!1),k.useContext(Vi).location}function af(e){k.useContext(cn).static||k.useLayoutEffect(e)}function nm(){let{isDataRoute:e}=k.useContext(dn);return e?gm():rm()}function rm(){ss()||ee(!1);let e=k.useContext(Bl),{basename:t,future:n,navigator:r}=k.useContext(cn),{matches:s}=k.useContext(dn),{pathname:i}=is(),o=JSON.stringify(nf(s,n.v7_relativeSplatPath)),a=k.useRef(!1);return af(()=>{a.current=!0}),k.useCallback(function(u,d){if(d===void 0&&(d={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let h=rf(u,JSON.parse(o),i,d.relative==="path");e==null&&t!=="/"&&(h.pathname=h.pathname==="/"?t:It([t,h.pathname])),(d.replace?r.replace:r.push)(h,d.state,d)},[t,r,o,i,e])}function lf(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=k.useContext(cn),{matches:s}=k.useContext(dn),{pathname:i}=is(),o=JSON.stringify(nf(s,r.v7_relativeSplatPath));return k.useMemo(()=>rf(e,JSON.parse(o),i,n==="path"),[e,o,i,n])}function sm(e,t){return im(e,t)}function im(e,t,n,r){ss()||ee(!1);let{navigator:s}=k.useContext(cn),{matches:i}=k.useContext(dn),o=i[i.length-1],a=o?o.params:{};o&&o.pathname;let l=o?o.pathnameBase:"/";o&&o.route;let u=is(),d;if(t){var h;let _=typeof t=="string"?Zn(t):t;l==="/"||(h=_.pathname)!=null&&h.startsWith(l)||ee(!1),d=_}else d=u;let f=d.pathname||"/",y=f;if(l!=="/"){let _=l.replace(/^\//,"").split("/");y="/"+f.replace(/^\//,"").split("/").slice(_.length).join("/")}let v=Tg(e,{pathname:y}),x=cm(v&&v.map(_=>Object.assign({},_,{params:Object.assign({},a,_.params),pathname:It([l,s.encodeLocation?s.encodeLocation(_.pathname).pathname:_.pathname]),pathnameBase:_.pathnameBase==="/"?l:It([l,s.encodeLocation?s.encodeLocation(_.pathnameBase).pathname:_.pathnameBase])})),i,n,r);return t&&x?k.createElement(Vi.Provider,{value:{location:Xr({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:Pt.Pop}},x):x}function om(){let e=pm(),t=Yg(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,s={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return k.createElement(k.Fragment,null,k.createElement("h2",null,"Unexpected Application Error!"),k.createElement("h3",{style:{fontStyle:"italic"}},t),n?k.createElement("pre",{style:s},n):null,null)}const am=k.createElement(om,null);class lm extends k.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?k.createElement(dn.Provider,{value:this.props.routeContext},k.createElement(of.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function um(e){let{routeContext:t,match:n,children:r}=e,s=k.useContext(Bl);return s&&s.static&&s.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=n.route.id),k.createElement(dn.Provider,{value:t},r)}function cm(e,t,n,r){var s;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let o=e,a=(s=n)==null?void 0:s.errors;if(a!=null){let d=o.findIndex(h=>h.route.id&&(a==null?void 0:a[h.route.id])!==void 0);d>=0||ee(!1),o=o.slice(0,Math.min(o.length,d+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<o.length;d++){let h=o[d];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(u=d),h.route.id){let{loaderData:f,errors:y}=n,v=h.route.loader&&f[h.route.id]===void 0&&(!y||y[h.route.id]===void 0);if(h.route.lazy||v){l=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((d,h,f)=>{let y,v=!1,x=null,_=null;n&&(y=a&&h.route.id?a[h.route.id]:void 0,x=h.route.errorElement||am,l&&(u<0&&f===0?(mm("route-fallback"),v=!0,_=null):u===f&&(v=!0,_=h.route.hydrateFallbackElement||null)));let m=t.concat(o.slice(0,f+1)),p=()=>{let g;return y?g=x:v?g=_:h.route.Component?g=k.createElement(h.route.Component,null):h.route.element?g=h.route.element:g=d,k.createElement(um,{match:h,routeContext:{outlet:d,matches:m,isDataRoute:n!=null},children:g})};return n&&(h.route.ErrorBoundary||h.route.errorElement||f===0)?k.createElement(lm,{location:n.location,revalidation:n.revalidation,component:x,error:y,children:p(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):p()},null)}var uf=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(uf||{}),cf=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(cf||{});function dm(e){let t=k.useContext(Bl);return t||ee(!1),t}function hm(e){let t=k.useContext(em);return t||ee(!1),t}function fm(e){let t=k.useContext(dn);return t||ee(!1),t}function df(e){let t=fm(),n=t.matches[t.matches.length-1];return n.route.id||ee(!1),n.route.id}function pm(){var e;let t=k.useContext(of),n=hm(),r=df();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function gm(){let{router:e}=dm(uf.UseNavigateStable),t=df(cf.UseNavigateStable),n=k.useRef(!1);return af(()=>{n.current=!0}),k.useCallback(function(s,i){i===void 0&&(i={}),n.current&&(typeof s=="number"?e.navigate(s):e.navigate(s,Xr({fromRouteId:t},i)))},[e,t])}const uc={};function mm(e,t,n){uc[e]||(uc[e]=!0)}function ym(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function Xs(e){ee(!1)}function vm(e){let{basename:t="/",children:n=null,location:r,navigationType:s=Pt.Pop,navigator:i,static:o=!1,future:a}=e;ss()&&ee(!1);let l=t.replace(/^\/*/,"/"),u=k.useMemo(()=>({basename:l,navigator:i,static:o,future:Xr({v7_relativeSplatPath:!1},a)}),[l,a,i,o]);typeof r=="string"&&(r=Zn(r));let{pathname:d="/",search:h="",hash:f="",state:y=null,key:v="default"}=r,x=k.useMemo(()=>{let _=Ul(d,l);return _==null?null:{location:{pathname:_,search:h,hash:f,state:y,key:v},navigationType:s}},[l,d,h,f,y,v,s]);return x==null?null:k.createElement(cn.Provider,{value:u},k.createElement(Vi.Provider,{children:n,value:x}))}function wm(e){let{children:t,location:n}=e;return sm(Na(t),n)}new Promise(()=>{});function Na(e,t){t===void 0&&(t=[]);let n=[];return k.Children.forEach(e,(r,s)=>{if(!k.isValidElement(r))return;let i=[...t,s];if(r.type===k.Fragment){n.push.apply(n,Na(r.props.children,i));return}r.type!==Xs&&ee(!1),!r.props.index||!r.props.children||ee(!1);let o={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=Na(r.props.children,i)),n.push(o)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ta(){return Ta=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ta.apply(this,arguments)}function xm(e,t){if(e==null)return{};var n={},r=Object.keys(e),s,i;for(i=0;i<r.length;i++)s=r[i],!(t.indexOf(s)>=0)&&(n[s]=e[s]);return n}function _m(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function km(e,t){return e.button===0&&(!t||t==="_self")&&!_m(e)}const Sm=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],bm="6";try{window.__reactRouterVersion=bm}catch{}const Cm="startTransition",cc=mp[Cm];function Pm(e){let{basename:t,children:n,future:r,window:s}=e,i=k.useRef();i.current==null&&(i.current=Eg({window:s,v5Compat:!0}));let o=i.current,[a,l]=k.useState({action:o.action,location:o.location}),{v7_startTransition:u}=r||{},d=k.useCallback(h=>{u&&cc?cc(()=>l(h)):l(h)},[l,u]);return k.useLayoutEffect(()=>o.listen(d),[o,d]),k.useEffect(()=>ym(r),[r]),k.createElement(vm,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:o,future:r})}const jm=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Em=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,vr=k.forwardRef(function(t,n){let{onClick:r,relative:s,reloadDocument:i,replace:o,state:a,target:l,to:u,preventScrollReset:d,viewTransition:h}=t,f=xm(t,Sm),{basename:y}=k.useContext(cn),v,x=!1;if(typeof u=="string"&&Em.test(u)&&(v=u,jm))try{let g=new URL(window.location.href),w=u.startsWith("//")?new URL(g.protocol+u):new URL(u),S=Ul(w.pathname,y);w.origin===g.origin&&S!=null?u=S+w.search+w.hash:x=!0}catch{}let _=tm(u,{relative:s}),m=Mm(u,{replace:o,state:a,target:l,preventScrollReset:d,relative:s,viewTransition:h});function p(g){r&&r(g),g.defaultPrevented||m(g)}return k.createElement("a",Ta({},f,{href:v||_,onClick:x||i?r:p,ref:n,target:l}))});var dc;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(dc||(dc={}));var hc;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(hc||(hc={}));function Mm(e,t){let{target:n,replace:r,state:s,preventScrollReset:i,relative:o,viewTransition:a}=t===void 0?{}:t,l=nm(),u=is(),d=lf(e,{relative:o});return k.useCallback(h=>{if(km(h,n)){h.preventDefault();let f=r!==void 0?r:Si(u)===Si(d);l(e,{replace:f,state:s,preventScrollReset:i,relative:o,viewTransition:a})}},[u,l,d,r,s,n,e,i,o,a])}const Ys=[{name:"Pangea Restaurant & Bar",category:"Food",city:"Garland",website:"https://pangeadfw.com",phone:"(*************",address:"2411 N Forest Rd, Garland, TX 75042",description:"Authentic African and Caribbean cuisine featuring traditional dishes with modern flair in a vibrant atmosphere.",rating:4.5,reviewCount:127,price:"$$",features:["Live Music","Outdoor Seating","Full Bar","Vegetarian Options"],hours:{monday:"11:00 AM - 10:00 PM",tuesday:"11:00 AM - 10:00 PM",wednesday:"11:00 AM - 10:00 PM",thursday:"11:00 AM - 11:00 PM",friday:"11:00 AM - 12:00 AM",saturday:"11:00 AM - 12:00 AM",sunday:"12:00 PM - 9:00 PM"},social:{instagram:"https://instagram.com/pangeadfw",facebook:"https://facebook.com/pangeadfw"}},{name:"Taste Community Restaurant",category:"Food",city:"Fort Worth",website:"https://tastproject.org",phone:"(*************",address:"1200 W Magnolia Ave, Fort Worth, TX 76104",description:"Community-focused restaurant providing job training and serving delicious Southern comfort food.",rating:4.7,reviewCount:95,price:"$",features:["Community Impact","Job Training Program","Southern Comfort Food"],hours:{tuesday:"11:00 AM - 8:00 PM",wednesday:"11:00 AM - 8:00 PM",thursday:"11:00 AM - 8:00 PM",friday:"11:00 AM - 8:00 PM",saturday:"10:00 AM - 8:00 PM",sunday:"10:00 AM - 3:00 PM"}},{name:"Brunchaholics",category:"Food",city:"DeSoto",website:"https://brunchaholics.com",phone:"(*************",address:"208 North Hampton Drive, Suite A, DeSoto, TX 75115",description:"Gourmet brunch spot specializing in creative breakfast and lunch dishes with a modern twist.",rating:4.3,reviewCount:68,price:"$$",features:["Weekend Brunch","Bottomless Mimosas","Vegan Options"],social:{instagram:"https://instagram.com/brunchaholics"}},{name:"Kessler Baking Studio",category:"Food",city:"Dallas",website:"https://kesslerbakingstudio.com",description:"Artisan bakery specializing in custom cakes, pastries, and fresh baked goods.",rating:4.8,reviewCount:152,price:"$",features:["Custom Cakes","Wedding Cakes","Fresh Daily Baking","Gluten-Free Options"]},{name:"Sweet Georgia Brown",category:"Food",city:"Dallas",phone:"(*************",address:"2840 E Ledbetter Dr, Dallas, TX 75216",description:"Traditional soul food restaurant serving authentic Southern cuisine passed down through generations.",rating:4.4,reviewCount:89,price:"$",features:["Soul Food","Family Recipes","Comfort Food"],hours:{monday:"11:00 AM - 8:00 PM",tuesday:"11:00 AM - 8:00 PM",wednesday:"11:00 AM - 8:00 PM",thursday:"11:00 AM - 8:00 PM",friday:"11:00 AM - 8:00 PM",saturday:"11:00 AM - 8:00 PM",sunday:"Closed"}},{name:"Black Coffee",category:"Cafe",city:"Fort Worth",website:"https://blackcoffeefw.com",address:"1417 Vaughn Boulevard, Fort Worth, TX 76105",description:"Specialty coffee shop with locally roasted beans, pastries, and community workspace.",rating:4.6,reviewCount:134,price:"$",features:["Locally Roasted","Free WiFi","Study Space","Pastries"],hours:{monday:"7:00 AM - 3:00 PM",tuesday:"7:00 AM - 3:00 PM",wednesday:"7:00 AM - 3:00 PM",thursday:"7:00 AM - 3:00 PM",friday:"7:00 AM - 3:00 PM",saturday:"7:00 AM - 3:00 PM",sunday:"8:00 AM - 3:00 PM"}},{name:"Soar Hair Extensions",category:"Beauty",city:"Arlington",description:"Premium hair extension services with high-quality virgin hair and professional styling.",rating:4.5,reviewCount:76,price:"$$$",features:["Virgin Hair","Custom Color","Styling Services"]},{name:"Lawren Taylor Skin Studio",category:"Beauty",city:"Dallas",description:"Professional skincare and aesthetic services including facials, treatments, and consultations.",rating:4.9,reviewCount:43,price:"$$$",features:["Facials","Skin Treatments","Aesthetic Services","Consultations"]},{name:"The Virgin Hair Fantasy",category:"Beauty",city:"Dallas",website:"https://thevirginhairfantasy.com",description:"Luxury hair extension boutique offering premium virgin hair and custom installation services.",rating:4.7,reviewCount:91,price:"$$$",features:["Virgin Hair","Custom Installation","Color Matching","Maintenance Services","Online Orders"]},{name:"The Barber Shop on Lamar",category:"Barbershop",city:"Dallas",description:"Traditional barbershop offering classic cuts, beard trims, and grooming services.",rating:4.2,reviewCount:67,price:"$",features:["Classic Cuts","Beard Trims","Hot Towel Service"]},{name:"Kingsmen Grooming",category:"Barbershop",city:"Arlington",description:"Modern barbershop specializing in contemporary cuts and premium grooming services.",rating:4.4,reviewCount:58,price:"$$",features:["Modern Cuts","Beard Styling","Premium Products"]},{name:"Cut Creators",category:"Barbershop",city:"Fort Worth",description:"Creative barbershop known for custom designs, fades, and artistic hair styling.",rating:4.6,reviewCount:102,price:"$$",features:["Custom Designs","Fades","Artistic Styling"]},{name:"Pan-African Connection Bookstore & Resource Center",category:"Bookstore",city:"Dallas",website:"https://www.panafricanconnection.com",phone:"(*************",address:"4466 S. Marsalis Ave, Dallas, TX 75216",description:"Independent bookstore specializing in African and African-American literature, history, and cultural resources.",rating:4.8,reviewCount:89,price:"$",features:["Independent Bookstore","Cultural Resources","Community Events","Rare Books"],hours:{monday:"Closed",tuesday:"11:00 AM - 7:00 PM",wednesday:"11:00 AM - 7:00 PM",thursday:"11:00 AM - 7:00 PM",friday:"11:00 AM - 7:00 PM",saturday:"10:00 AM - 7:00 PM",sunday:"1:00 PM - 5:00 PM"}},{name:"Black Lit Bookstore",category:"Bookstore",city:"Dallas",description:"Curated collection of books by and about Black authors, focusing on literature, poetry, and non-fiction.",rating:4.5,reviewCount:34,price:"$",features:["Black Authors","Poetry","Literature","Community Readings"]},{name:"Cool Beans Coffee & Custard",category:"Cafe",city:"Arlington",description:"Family-owned cafe serving specialty coffee, homemade custard, and light breakfast options.",rating:4.3,reviewCount:45,price:"$",features:["Homemade Custard","Family Owned","Light Breakfast"]},{name:"Soar Fitness",category:"Fitness",city:"Dallas",description:"Full-service fitness center with personal training, group classes, and wellness programs.",rating:4.4,reviewCount:78,price:"$$",features:["Personal Training","Group Classes","Wellness Programs"]},{name:"Oak Cliff Coffee Roasters",category:"Cafe",city:"Dallas",website:"https://oakcliffcoffee.com",phone:"(*************",address:"817 W Davis St #120, Dallas, TX 75208",description:"Local coffee roastery with fresh roasted beans, pour-over coffee, and artisanal pastries.",rating:4.7,reviewCount:156,price:"$$",features:["Local Roastery","Pour-over Coffee","Artisanal Pastries","Fresh Roasted"],hours:{monday:"6:00 AM - 4:00 PM",tuesday:"6:00 AM - 4:00 PM",wednesday:"6:00 AM - 4:00 PM",thursday:"6:00 AM - 4:00 PM",friday:"6:00 AM - 4:00 PM",saturday:"6:00 AM - 4:00 PM",sunday:"6:00 AM - 4:00 PM"}},{name:"Brown Sugar Beauty Supply",category:"Retail",city:"Fort Worth",description:"Beauty supply store specializing in products for natural and textured hair care.",rating:4.2,reviewCount:67,price:"$",features:["Natural Hair Products","Textured Hair Care","Professional Supplies"]},{name:"We the People Barber Shop",category:"Barbershop",city:"Grand Prairie",description:"Community barbershop providing quality cuts and grooming services in a welcoming environment.",rating:4.3,reviewCount:52,price:"$",features:["Community Focused","Quality Cuts","Welcoming Environment"]},{name:"Fachini",category:"Food",city:"Dallas",website:"https://fachinirestaurant.com",phone:"(*************",address:"2021 Greenville Ave, Dallas, TX 75206",description:"Upscale Italian restaurant with a contemporary twist, featuring handmade pasta and artisanal cocktails.",rating:4.6,reviewCount:189,price:"$$$",features:["Handmade Pasta","Craft Cocktails","Date Night","Fine Dining"],hours:{tuesday:"5:00 PM - 10:00 PM",wednesday:"5:00 PM - 10:00 PM",thursday:"5:00 PM - 10:00 PM",friday:"5:00 PM - 11:00 PM",saturday:"5:00 PM - 11:00 PM",sunday:"5:00 PM - 9:00 PM"},social:{instagram:"https://instagram.com/fachinirestaurant",facebook:"https://facebook.com/fachinirestaurant"}},{name:"Soho Coffee Bar",category:"Cafe",city:"Dallas",website:"https://sohocoffeebar.com",phone:"(*************",address:"1422 S Lamar St, Dallas, TX 75215",description:"Specialty coffee shop in the heart of South Dallas serving artisan coffee and light bites.",rating:4.4,reviewCount:98,price:"$",features:["Specialty Coffee","Local Art","Community Space","Wi-Fi Friendly"],hours:{monday:"6:00 AM - 3:00 PM",tuesday:"6:00 AM - 3:00 PM",wednesday:"6:00 AM - 3:00 PM",thursday:"6:00 AM - 3:00 PM",friday:"6:00 AM - 3:00 PM",saturday:"7:00 AM - 3:00 PM",sunday:"8:00 AM - 2:00 PM"}},{name:"Poppa Rollo's Pizza",category:"Food",city:"Grand Prairie",phone:"(*************",address:"819 W Interstate 20, Grand Prairie, TX 75052",description:"Family-owned pizza restaurant serving New York-style pizza with fresh ingredients and generous toppings.",rating:4.3,reviewCount:234,price:"$$",features:["New York Style Pizza","Family Owned","Fresh Ingredients","Generous Portions"],hours:{monday:"11:00 AM - 9:00 PM",tuesday:"11:00 AM - 9:00 PM",wednesday:"11:00 AM - 9:00 PM",thursday:"11:00 AM - 9:00 PM",friday:"11:00 AM - 10:00 PM",saturday:"11:00 AM - 10:00 PM",sunday:"12:00 PM - 8:00 PM"}},{name:"Juneteenth Ice Cream & Treats",category:"Food",city:"Fort Worth",phone:"(*************",address:"1300 E Lancaster Ave, Fort Worth, TX 76102",description:"Specialty ice cream shop celebrating African American culture with unique flavors and nostalgic treats.",rating:4.7,reviewCount:156,price:"$",features:["Unique Flavors","Cultural Celebration","Nostalgic Treats","Family Friendly"],hours:{tuesday:"12:00 PM - 8:00 PM",wednesday:"12:00 PM - 8:00 PM",thursday:"12:00 PM - 8:00 PM",friday:"12:00 PM - 9:00 PM",saturday:"11:00 AM - 9:00 PM",sunday:"12:00 PM - 7:00 PM"},social:{instagram:"https://instagram.com/juneteenthicecream"}},{name:"Kemp's Burgers",category:"Food",city:"Dallas",phone:"(*************",address:"3621 Parry Ave, Dallas, TX 75226",description:"Local burger joint serving classic American burgers with fresh ingredients and homemade fries since 1985.",rating:4.5,reviewCount:287,price:"$",features:["Since 1985","Homemade Fries","Classic Burgers","Local Institution"],hours:{monday:"10:00 AM - 8:00 PM",tuesday:"10:00 AM - 8:00 PM",wednesday:"10:00 AM - 8:00 PM",thursday:"10:00 AM - 8:00 PM",friday:"10:00 AM - 9:00 PM",saturday:"10:00 AM - 9:00 PM",sunday:"11:00 AM - 7:00 PM"}},{name:"Yum Yum Tree Restaurant",category:"Food",city:"Dallas",phone:"(*************",address:"616 N Bishop Ave, Dallas, TX 75208",description:"Neighborhood soul food restaurant serving authentic Southern comfort food and homestyle cooking.",rating:4.2,reviewCount:134,price:"$",features:["Soul Food","Homestyle Cooking","Southern Comfort Food","Neighborhood Favorite"],hours:{tuesday:"11:00 AM - 8:00 PM",wednesday:"11:00 AM - 8:00 PM",thursday:"11:00 AM - 8:00 PM",friday:"11:00 AM - 9:00 PM",saturday:"11:00 AM - 9:00 PM",sunday:"12:00 PM - 7:00 PM"}},{name:"AllGood Cafe",category:"Food",city:"Dallas",website:"https://allgoodcafe.com",phone:"(*************",address:"2934 Main St, Dallas, TX 75226",description:"Health-focused cafe serving organic salads, smoothies, and fresh juices with vegan and vegetarian options.",rating:4.6,reviewCount:172,price:"$$",features:["Organic","Vegan Options","Fresh Juices","Health Focused"],hours:{monday:"7:00 AM - 7:00 PM",tuesday:"7:00 AM - 7:00 PM",wednesday:"7:00 AM - 7:00 PM",thursday:"7:00 AM - 7:00 PM",friday:"7:00 AM - 7:00 PM",saturday:"8:00 AM - 6:00 PM",sunday:"9:00 AM - 5:00 PM"},social:{instagram:"https://instagram.com/allgoodcafedallas"}},{name:"Brookshire's by Pecan Lodge",category:"Food",city:"Dallas",website:"https://brookshiresgrocery.com",phone:"(*************",address:"Multiple Locations",description:"Grocery chain featuring a special partnership with Pecan Lodge for premium BBQ and prepared foods.",rating:4.1,reviewCount:89,price:"$$",features:["Pecan Lodge Partnership","Premium BBQ","Prepared Foods","Multiple Locations"]},{name:"Whistle Britches",category:"Food",city:"Dallas",website:"https://whistlebritches.com",phone:"(*************",address:"2810 N Harwood St, Dallas, TX 75201",description:"Gourmet fried chicken restaurant with Southern-inspired sides and craft cocktails in a modern setting.",rating:4.5,reviewCount:245,price:"$$",features:["Gourmet Fried Chicken","Craft Cocktails","Southern Inspired","Modern Setting"],hours:{monday:"5:00 PM - 10:00 PM",tuesday:"5:00 PM - 10:00 PM",wednesday:"5:00 PM - 10:00 PM",thursday:"5:00 PM - 10:00 PM",friday:"5:00 PM - 11:00 PM",saturday:"11:00 AM - 11:00 PM",sunday:"11:00 AM - 9:00 PM"}},{name:"TC Shaved Ice & More",category:"Food",city:"Irving",phone:"(*************",address:"1405 W Airport Fwy, Irving, TX 75062",description:"Family-friendly shaved ice stand with over 50 flavors and specialty treats for all seasons.",rating:4.8,reviewCount:93,price:"$",features:["50+ Flavors","Family Friendly","Specialty Treats","All Seasons"],hours:{monday:"12:00 PM - 9:00 PM",tuesday:"12:00 PM - 9:00 PM",wednesday:"12:00 PM - 9:00 PM",thursday:"12:00 PM - 9:00 PM",friday:"12:00 PM - 10:00 PM",saturday:"11:00 AM - 10:00 PM",sunday:"12:00 PM - 8:00 PM"}},{name:"The Velvet Taco",category:"Food",city:"Dallas",website:"https://thevelvettaco.com",phone:"(*************",address:"3012 Elm St, Dallas, TX 75226",description:"Creative taco shop featuring globally-inspired tacos with unique flavor combinations and late-night hours.",rating:4.4,reviewCount:312,price:"$$",features:["Globally Inspired","Late Night Hours","Unique Flavors","Creative Tacos"],hours:{sunday:"10:00 AM - 2:00 AM",monday:"10:00 AM - 2:00 AM",tuesday:"10:00 AM - 2:00 AM",wednesday:"10:00 AM - 2:00 AM",thursday:"10:00 AM - 2:00 AM",friday:"10:00 AM - 3:00 AM",saturday:"10:00 AM - 3:00 AM"}},{name:"BurgerIM",category:"Food",city:"Plano",phone:"(*************",address:"1201 E Spring Creek Pkwy #172, Plano, TX 75074",description:"Gourmet burger restaurant offering customizable burgers with premium ingredients and hand-cut fries.",rating:4.2,reviewCount:178,price:"$$",features:["Customizable Burgers","Premium Ingredients","Hand-cut Fries","Gourmet Options"],hours:{sunday:"11:00 AM - 10:00 PM",monday:"11:00 AM - 10:00 PM",tuesday:"11:00 AM - 10:00 PM",wednesday:"11:00 AM - 10:00 PM",thursday:"11:00 AM - 10:00 PM",friday:"11:00 AM - 11:00 PM",saturday:"11:00 AM - 11:00 PM"}},{name:"Simply Fondue",category:"Food",city:"Dallas",website:"https://simplyfondue.com",phone:"(*************",address:"4152 Cole Ave Suite 104, Dallas, TX 75204",description:"Upscale fondue restaurant perfect for romantic dinners and special occasions with cheese and chocolate fondues.",rating:4.7,reviewCount:221,price:"$$$",features:["Romantic Dining","Cheese Fondue","Chocolate Fondue","Special Occasions"],hours:{tuesday:"5:00 PM - 10:00 PM",wednesday:"5:00 PM - 10:00 PM",thursday:"5:00 PM - 10:00 PM",friday:"5:00 PM - 11:00 PM",saturday:"5:00 PM - 11:00 PM",sunday:"4:00 PM - 9:00 PM"}},{name:"Slutty Vegan DFW",category:"Food",city:"Dallas",website:"https://thesluttyvegan.com",phone:"(*************",address:"2019 Greenville Ave, Dallas, TX 75206",description:"Popular vegan burger chain known for creative plant-based burgers with bold flavors and Instagram-worthy presentation.",rating:4.3,reviewCount:167,price:"$$",features:["Plant-Based","Creative Burgers","Bold Flavors","Instagram Worthy"],hours:{sunday:"12:00 PM - 10:00 PM",monday:"12:00 PM - 10:00 PM",tuesday:"12:00 PM - 10:00 PM",wednesday:"12:00 PM - 10:00 PM",thursday:"12:00 PM - 10:00 PM",friday:"12:00 PM - 11:00 PM",saturday:"12:00 PM - 11:00 PM"},social:{instagram:"https://instagram.com/thesluttyvegan"}},{name:"Chop Shop Burgers & Fries",category:"Food",city:"Arlington",phone:"(*************",address:"604 E Lamar Blvd, Arlington, TX 76011",description:"Gourmet burger restaurant specializing in fresh ground beef patties and hand-cut fries with unique toppings.",rating:4.4,reviewCount:189,price:"$$",features:["Fresh Ground Beef","Hand-cut Fries","Unique Toppings","Gourmet Burgers"],hours:{monday:"11:00 AM - 9:00 PM",tuesday:"11:00 AM - 9:00 PM",wednesday:"11:00 AM - 9:00 PM",thursday:"11:00 AM - 9:00 PM",friday:"11:00 AM - 10:00 PM",saturday:"11:00 AM - 10:00 PM",sunday:"12:00 PM - 8:00 PM"}},{name:"Royalty Bookstore & Coffee House",category:"Retail",city:"Dallas",phone:"(*************",address:"3132 Martin Luther King Jr Blvd, Dallas, TX 75215",description:"Independent bookstore specializing in African American literature with a cozy coffee house atmosphere.",rating:4.8,reviewCount:96,price:"$",features:["African American Literature","Coffee House","Independent Bookstore","Community Events"],hours:{monday:"9:00 AM - 7:00 PM",tuesday:"9:00 AM - 7:00 PM",wednesday:"9:00 AM - 7:00 PM",thursday:"9:00 AM - 8:00 PM",friday:"9:00 AM - 8:00 PM",saturday:"9:00 AM - 8:00 PM",sunday:"12:00 PM - 6:00 PM"}},{name:"Black Pearl Salon",category:"Beauty",city:"Dallas",phone:"(*************",address:"2914 Martin Luther King Jr Blvd, Dallas, TX 75215",description:"Full-service hair salon specializing in natural hair care, relaxers, and protective styling.",rating:4.5,reviewCount:143,price:"$$",features:["Natural Hair Care","Relaxers","Protective Styling","Full Service"],hours:{tuesday:"10:00 AM - 6:00 PM",wednesday:"10:00 AM - 6:00 PM",thursday:"10:00 AM - 7:00 PM",friday:"10:00 AM - 7:00 PM",saturday:"9:00 AM - 5:00 PM"}},{name:"Heritage Farmstead Museum Gift Shop",category:"Retail",city:"Plano",website:"https://heritagefarmstead.org",phone:"(*************",address:"1900 W 15th St, Plano, TX 75075",description:"Museum gift shop featuring locally-made crafts, historical books, and educational toys.",rating:4.3,reviewCount:67,price:"$",features:["Locally Made Crafts","Historical Books","Educational Toys","Museum Quality"]},{name:"Signature Nails & Spa",category:"Beauty",city:"Mesquite",phone:"(*************",address:"1234 Military Pkwy, Mesquite, TX 75149",description:"Full-service nail salon and spa offering manicures, pedicures, and relaxing spa treatments.",rating:4.2,reviewCount:112,price:"$$",features:["Full Service","Spa Treatments","Relaxing Environment","Professional Staff"],hours:{monday:"9:00 AM - 7:00 PM",tuesday:"9:00 AM - 7:00 PM",wednesday:"9:00 AM - 7:00 PM",thursday:"9:00 AM - 7:00 PM",friday:"9:00 AM - 7:00 PM",saturday:"9:00 AM - 6:00 PM",sunday:"12:00 PM - 5:00 PM"}},{name:"Diamond's Barbershop",category:"Barbershop",city:"Fort Worth",phone:"(*************",address:"3401 E Lancaster Ave, Fort Worth, TX 76103",description:"Traditional barbershop offering classic cuts, hot towel shaves, and beard grooming services.",rating:4.6,reviewCount:98,price:"$",features:["Classic Cuts","Hot Towel Shaves","Beard Grooming","Traditional Service"],hours:{tuesday:"9:00 AM - 6:00 PM",wednesday:"9:00 AM - 6:00 PM",thursday:"9:00 AM - 6:00 PM",friday:"9:00 AM - 6:00 PM",saturday:"8:00 AM - 4:00 PM"}},{name:"Elite Automotive Services",category:"Automotive",city:"Dallas",phone:"(*************",address:"4567 S Lancaster Rd, Dallas, TX 75216",description:"Full-service automotive repair shop specializing in foreign and domestic vehicles with honest pricing.",rating:4.4,reviewCount:187,price:"$$",features:["Foreign & Domestic","Honest Pricing","Full Service","Experienced Mechanics"],hours:{monday:"7:00 AM - 6:00 PM",tuesday:"7:00 AM - 6:00 PM",wednesday:"7:00 AM - 6:00 PM",thursday:"7:00 AM - 6:00 PM",friday:"7:00 AM - 6:00 PM",saturday:"8:00 AM - 4:00 PM"}},{name:"Trinity River Fitness",category:"Fitness",city:"Dallas",website:"https://trinityriverfitness.com",phone:"(*************",address:"2100 Commerce St, Dallas, TX 75201",description:"Modern fitness center offering personal training, group classes, and state-of-the-art equipment.",rating:4.5,reviewCount:203,price:"$$",features:["Personal Training","Group Classes","State-of-the-Art Equipment","Modern Facility"],hours:{monday:"5:00 AM - 11:00 PM",tuesday:"5:00 AM - 11:00 PM",wednesday:"5:00 AM - 11:00 PM",thursday:"5:00 AM - 11:00 PM",friday:"5:00 AM - 10:00 PM",saturday:"6:00 AM - 8:00 PM",sunday:"7:00 AM - 8:00 PM"}},{name:"Queen City Bakery",category:"Food",city:"Dallas",phone:"(*************",address:"1834 Martin Luther King Jr Blvd, Dallas, TX 75215",description:"Traditional bakery specializing in custom cakes, fresh pastries, and Southern-style desserts.",rating:4.7,reviewCount:134,price:"$",features:["Custom Cakes","Fresh Pastries","Southern Desserts","Traditional Recipes"],hours:{tuesday:"6:00 AM - 6:00 PM",wednesday:"6:00 AM - 6:00 PM",thursday:"6:00 AM - 6:00 PM",friday:"6:00 AM - 7:00 PM",saturday:"6:00 AM - 7:00 PM",sunday:"7:00 AM - 4:00 PM"}},{name:"Mahogany Tax Services",category:"Professional Services",city:"Garland",phone:"(*************",address:"5421 Broadway Blvd, Garland, TX 75043",description:"Professional tax preparation and accounting services for individuals and small businesses.",rating:4.8,reviewCount:76,price:"$$",features:["Tax Preparation","Small Business Accounting","Individual Services","Year-Round Support"],hours:{monday:"9:00 AM - 7:00 PM",tuesday:"9:00 AM - 7:00 PM",wednesday:"9:00 AM - 7:00 PM",thursday:"9:00 AM - 7:00 PM",friday:"9:00 AM - 6:00 PM",saturday:"9:00 AM - 4:00 PM"}},{name:"Urban Threads Boutique",category:"Retail",city:"Arlington",phone:"(*************",address:"2567 W Division St, Arlington, TX 76012",description:"Trendy clothing boutique featuring contemporary fashion for men and women with affordable prices.",rating:4.3,reviewCount:89,price:"$$",features:["Contemporary Fashion","Men & Women","Affordable Prices","Trendy Styles"],hours:{monday:"10:00 AM - 8:00 PM",tuesday:"10:00 AM - 8:00 PM",wednesday:"10:00 AM - 8:00 PM",thursday:"10:00 AM - 8:00 PM",friday:"10:00 AM - 9:00 PM",saturday:"10:00 AM - 9:00 PM",sunday:"12:00 PM - 6:00 PM"}},{name:"Cornerstone Community Development",category:"Professional Services",city:"Dallas",website:"https://cornerstonecommunitydev.org",phone:"(*************",address:"2800 South Lancaster Rd, Dallas, TX 75216",description:"Non-profit organization providing community development services, financial literacy, and homeownership counseling.",rating:4.9,reviewCount:45,price:"$",features:["Community Development","Financial Literacy","Homeownership Counseling","Non-Profit"],hours:{monday:"8:00 AM - 5:00 PM",tuesday:"8:00 AM - 5:00 PM",wednesday:"8:00 AM - 5:00 PM",thursday:"8:00 AM - 5:00 PM",friday:"8:00 AM - 5:00 PM"}},{name:"Mosaic Family Services",category:"Professional Services",city:"Dallas",website:"https://mosaicservices.org",phone:"(*************",address:"12225 Greenville Ave #700, Dallas, TX 75243",description:"Family services organization providing adoption, counseling, and community support programs.",rating:4.6,reviewCount:34,price:"$",features:["Adoption Services","Counseling","Community Support","Family Focused"],hours:{monday:"8:00 AM - 5:00 PM",tuesday:"8:00 AM - 5:00 PM",wednesday:"8:00 AM - 5:00 PM",thursday:"8:00 AM - 5:00 PM",friday:"8:00 AM - 5:00 PM"}}],Nm="modulepreload",Tm=function(e){return"/"+e},fc={},os=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),a=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));s=Promise.allSettled(n.map(l=>{if(l=Tm(l),l in fc)return;fc[l]=!0;const u=l.endsWith(".css"),d=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${l}"]${d}`))return;const h=document.createElement("link");if(h.rel=u?"stylesheet":Nm,u||(h.as="script"),h.crossOrigin="",h.href=l,a&&h.setAttribute("nonce",a),document.head.appendChild(h),u)return new Promise((f,y)=>{h.addEventListener("load",f),h.addEventListener("error",()=>y(new Error(`Unable to preload CSS for ${l}`)))})}))}function i(o){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o}return s.then(o=>{for(const a of o||[])a.status==="rejected"&&i(a.reason);return t().catch(i)})},Am=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>os(async()=>{const{default:r}=await Promise.resolve().then(()=>er);return{default:r}},void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)};class Wl extends Error{constructor(t,n="FunctionsError",r){super(t),this.name=n,this.context=r}}class $m extends Wl{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class pc extends Wl{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class gc extends Wl{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var Aa;(function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"})(Aa||(Aa={}));var Om=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function a(d){try{u(r.next(d))}catch(h){o(h)}}function l(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class Im{constructor(t,{headers:n={},customFetch:r,region:s=Aa.Any}={}){this.url=t,this.headers=n,this.region=s,this.fetch=Am(r)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,n={}){var r;return Om(this,void 0,void 0,function*(){try{const{headers:s,method:i,body:o}=n;let a={},{region:l}=n;l||(l=this.region);const u=new URL(`${this.url}/${t}`);l&&l!=="any"&&(a["x-region"]=l,u.searchParams.set("forceFunctionRegion",l));let d;o&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&(typeof Blob<"u"&&o instanceof Blob||o instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",d=o):typeof o=="string"?(a["Content-Type"]="text/plain",d=o):typeof FormData<"u"&&o instanceof FormData?d=o:(a["Content-Type"]="application/json",d=JSON.stringify(o)));const h=yield this.fetch(u.toString(),{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),s),body:d}).catch(x=>{throw new $m(x)}),f=h.headers.get("x-relay-error");if(f&&f==="true")throw new pc(h);if(!h.ok)throw new gc(h);let y=((r=h.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),v;return y==="application/json"?v=yield h.json():y==="application/octet-stream"?v=yield h.blob():y==="text/event-stream"?v=h:y==="multipart/form-data"?v=yield h.formData():v=yield h.text(),{data:v,error:null,response:h}}catch(s){return{data:null,error:s,response:s instanceof gc||s instanceof pc?s.context:void 0}}})}}var xe={},Hl={},qi={},as={},Ki={},Gi={},Rm=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},Jn=Rm();const Lm=Jn.fetch,hf=Jn.fetch.bind(Jn),ff=Jn.Headers,Dm=Jn.Request,Fm=Jn.Response,er=Object.freeze(Object.defineProperty({__proto__:null,Headers:ff,Request:Dm,Response:Fm,default:hf,fetch:Lm},Symbol.toStringTag,{value:"Module"})),zm=ep(er);var Ji={};Object.defineProperty(Ji,"__esModule",{value:!0});let Um=class extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}};Ji.default=Um;var pf=Re&&Re.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Gi,"__esModule",{value:!0});const Bm=pf(zm),Wm=pf(Ji);let Hm=class{constructor(t){var n,r;this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=new Headers(t.headers),this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=(n=t.shouldThrowOnError)!==null&&n!==void 0?n:!1,this.signal=t.signal,this.isMaybeSingle=(r=t.isMaybeSingle)!==null&&r!==void 0?r:!1,t.fetch?this.fetch=t.fetch:typeof fetch>"u"?this.fetch=Bm.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(t,n){return this.headers=new Headers(this.headers),this.headers.set(t,n),this}then(t,n){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers.set("Accept-Profile",this.schema):this.headers.set("Content-Profile",this.schema)),this.method!=="GET"&&this.method!=="HEAD"&&this.headers.set("Content-Type","application/json");const r=this.fetch;let s=r(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async i=>{var o,a,l,u;let d=null,h=null,f=null,y=i.status,v=i.statusText;if(i.ok){if(this.method!=="HEAD"){const p=await i.text();p===""||(this.headers.get("Accept")==="text/csv"||this.headers.get("Accept")&&(!((o=this.headers.get("Accept"))===null||o===void 0)&&o.includes("application/vnd.pgrst.plan+text"))?h=p:h=JSON.parse(p))}const _=(a=this.headers.get("Prefer"))===null||a===void 0?void 0:a.match(/count=(exact|planned|estimated)/),m=(l=i.headers.get("content-range"))===null||l===void 0?void 0:l.split("/");_&&m&&m.length>1&&(f=parseInt(m[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(h)&&(h.length>1?(d={code:"PGRST116",details:`Results contain ${h.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},h=null,f=null,y=406,v="Not Acceptable"):h.length===1?h=h[0]:h=null)}else{const _=await i.text();try{d=JSON.parse(_),Array.isArray(d)&&i.status===404&&(h=[],d=null,y=200,v="OK")}catch{i.status===404&&_===""?(y=204,v="No Content"):d={message:_}}if(d&&this.isMaybeSingle&&(!((u=d==null?void 0:d.details)===null||u===void 0)&&u.includes("0 rows"))&&(d=null,y=200,v="OK"),d&&this.shouldThrowOnError)throw new Wm.default(d)}return{error:d,data:h,count:f,status:y,statusText:v}});return this.shouldThrowOnError||(s=s.catch(i=>{var o,a,l;return{error:{message:`${(o=i==null?void 0:i.name)!==null&&o!==void 0?o:"FetchError"}: ${i==null?void 0:i.message}`,details:`${(a=i==null?void 0:i.stack)!==null&&a!==void 0?a:""}`,hint:"",code:`${(l=i==null?void 0:i.code)!==null&&l!==void 0?l:""}`},data:null,count:null,status:0,statusText:""}})),s.then(t,n)}returns(){return this}overrideTypes(){return this}};Gi.default=Hm;var Vm=Re&&Re.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ki,"__esModule",{value:!0});const qm=Vm(Gi);let Km=class extends qm.default{select(t){let n=!1;const r=(t??"*").split("").map(s=>/\s/.test(s)&&!n?"":(s==='"'&&(n=!n),s)).join("");return this.url.searchParams.set("select",r),this.headers.append("Prefer","return=representation"),this}order(t,{ascending:n=!0,nullsFirst:r,foreignTable:s,referencedTable:i=s}={}){const o=i?`${i}.order`:"order",a=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${a?`${a},`:""}${t}.${n?"asc":"desc"}${r===void 0?"":r?".nullsfirst":".nullslast"}`),this}limit(t,{foreignTable:n,referencedTable:r=n}={}){const s=typeof r>"u"?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${t}`),this}range(t,n,{foreignTable:r,referencedTable:s=r}={}){const i=typeof s>"u"?"offset":`${s}.offset`,o=typeof s>"u"?"limit":`${s}.limit`;return this.url.searchParams.set(i,`${t}`),this.url.searchParams.set(o,`${n-t+1}`),this}abortSignal(t){return this.signal=t,this}single(){return this.headers.set("Accept","application/vnd.pgrst.object+json"),this}maybeSingle(){return this.method==="GET"?this.headers.set("Accept","application/json"):this.headers.set("Accept","application/vnd.pgrst.object+json"),this.isMaybeSingle=!0,this}csv(){return this.headers.set("Accept","text/csv"),this}geojson(){return this.headers.set("Accept","application/geo+json"),this}explain({analyze:t=!1,verbose:n=!1,settings:r=!1,buffers:s=!1,wal:i=!1,format:o="text"}={}){var a;const l=[t?"analyze":null,n?"verbose":null,r?"settings":null,s?"buffers":null,i?"wal":null].filter(Boolean).join("|"),u=(a=this.headers.get("Accept"))!==null&&a!==void 0?a:"application/json";return this.headers.set("Accept",`application/vnd.pgrst.plan+${o}; for="${u}"; options=${l};`),o==="json"?this:this}rollback(){return this.headers.append("Prefer","tx=rollback"),this}returns(){return this}maxAffected(t){return this.headers.append("Prefer","handling=strict"),this.headers.append("Prefer",`max-affected=${t}`),this}};Ki.default=Km;var Gm=Re&&Re.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(as,"__esModule",{value:!0});const Jm=Gm(Ki);let Qm=class extends Jm.default{eq(t,n){return this.url.searchParams.append(t,`eq.${n}`),this}neq(t,n){return this.url.searchParams.append(t,`neq.${n}`),this}gt(t,n){return this.url.searchParams.append(t,`gt.${n}`),this}gte(t,n){return this.url.searchParams.append(t,`gte.${n}`),this}lt(t,n){return this.url.searchParams.append(t,`lt.${n}`),this}lte(t,n){return this.url.searchParams.append(t,`lte.${n}`),this}like(t,n){return this.url.searchParams.append(t,`like.${n}`),this}likeAllOf(t,n){return this.url.searchParams.append(t,`like(all).{${n.join(",")}}`),this}likeAnyOf(t,n){return this.url.searchParams.append(t,`like(any).{${n.join(",")}}`),this}ilike(t,n){return this.url.searchParams.append(t,`ilike.${n}`),this}ilikeAllOf(t,n){return this.url.searchParams.append(t,`ilike(all).{${n.join(",")}}`),this}ilikeAnyOf(t,n){return this.url.searchParams.append(t,`ilike(any).{${n.join(",")}}`),this}is(t,n){return this.url.searchParams.append(t,`is.${n}`),this}in(t,n){const r=Array.from(new Set(n)).map(s=>typeof s=="string"&&new RegExp("[,()]").test(s)?`"${s}"`:`${s}`).join(",");return this.url.searchParams.append(t,`in.(${r})`),this}contains(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cs.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cs.{${n.join(",")}}`):this.url.searchParams.append(t,`cs.${JSON.stringify(n)}`),this}containedBy(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cd.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cd.{${n.join(",")}}`):this.url.searchParams.append(t,`cd.${JSON.stringify(n)}`),this}rangeGt(t,n){return this.url.searchParams.append(t,`sr.${n}`),this}rangeGte(t,n){return this.url.searchParams.append(t,`nxl.${n}`),this}rangeLt(t,n){return this.url.searchParams.append(t,`sl.${n}`),this}rangeLte(t,n){return this.url.searchParams.append(t,`nxr.${n}`),this}rangeAdjacent(t,n){return this.url.searchParams.append(t,`adj.${n}`),this}overlaps(t,n){return typeof n=="string"?this.url.searchParams.append(t,`ov.${n}`):this.url.searchParams.append(t,`ov.{${n.join(",")}}`),this}textSearch(t,n,{config:r,type:s}={}){let i="";s==="plain"?i="pl":s==="phrase"?i="ph":s==="websearch"&&(i="w");const o=r===void 0?"":`(${r})`;return this.url.searchParams.append(t,`${i}fts${o}.${n}`),this}match(t){return Object.entries(t).forEach(([n,r])=>{this.url.searchParams.append(n,`eq.${r}`)}),this}not(t,n,r){return this.url.searchParams.append(t,`not.${n}.${r}`),this}or(t,{foreignTable:n,referencedTable:r=n}={}){const s=r?`${r}.or`:"or";return this.url.searchParams.append(s,`(${t})`),this}filter(t,n,r){return this.url.searchParams.append(t,`${n}.${r}`),this}};as.default=Qm;var Xm=Re&&Re.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(qi,"__esModule",{value:!0});const dr=Xm(as);let Ym=class{constructor(t,{headers:n={},schema:r,fetch:s}){this.url=t,this.headers=new Headers(n),this.schema=r,this.fetch=s}select(t,{head:n=!1,count:r}={}){const s=n?"HEAD":"GET";let i=!1;const o=(t??"*").split("").map(a=>/\s/.test(a)&&!i?"":(a==='"'&&(i=!i),a)).join("");return this.url.searchParams.set("select",o),r&&this.headers.append("Prefer",`count=${r}`),new dr.default({method:s,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch})}insert(t,{count:n,defaultToNull:r=!0}={}){var s;const i="POST";if(n&&this.headers.append("Prefer",`count=${n}`),r||this.headers.append("Prefer","missing=default"),Array.isArray(t)){const o=t.reduce((a,l)=>a.concat(Object.keys(l)),[]);if(o.length>0){const a=[...new Set(o)].map(l=>`"${l}"`);this.url.searchParams.set("columns",a.join(","))}}return new dr.default({method:i,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:(s=this.fetch)!==null&&s!==void 0?s:fetch})}upsert(t,{onConflict:n,ignoreDuplicates:r=!1,count:s,defaultToNull:i=!0}={}){var o;const a="POST";if(this.headers.append("Prefer",`resolution=${r?"ignore":"merge"}-duplicates`),n!==void 0&&this.url.searchParams.set("on_conflict",n),s&&this.headers.append("Prefer",`count=${s}`),i||this.headers.append("Prefer","missing=default"),Array.isArray(t)){const l=t.reduce((u,d)=>u.concat(Object.keys(d)),[]);if(l.length>0){const u=[...new Set(l)].map(d=>`"${d}"`);this.url.searchParams.set("columns",u.join(","))}}return new dr.default({method:a,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:(o=this.fetch)!==null&&o!==void 0?o:fetch})}update(t,{count:n}={}){var r;const s="PATCH";return n&&this.headers.append("Prefer",`count=${n}`),new dr.default({method:s,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:(r=this.fetch)!==null&&r!==void 0?r:fetch})}delete({count:t}={}){var n;const r="DELETE";return t&&this.headers.append("Prefer",`count=${t}`),new dr.default({method:r,url:this.url,headers:this.headers,schema:this.schema,fetch:(n=this.fetch)!==null&&n!==void 0?n:fetch})}};qi.default=Ym;var gf=Re&&Re.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Hl,"__esModule",{value:!0});const Zm=gf(qi),ey=gf(as);let ty=class mf{constructor(t,{headers:n={},schema:r,fetch:s}={}){this.url=t,this.headers=new Headers(n),this.schemaName=r,this.fetch=s}from(t){const n=new URL(`${this.url}/${t}`);return new Zm.default(n,{headers:new Headers(this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new mf(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(t,n={},{head:r=!1,get:s=!1,count:i}={}){var o;let a;const l=new URL(`${this.url}/rpc/${t}`);let u;r||s?(a=r?"HEAD":"GET",Object.entries(n).filter(([h,f])=>f!==void 0).map(([h,f])=>[h,Array.isArray(f)?`{${f.join(",")}}`:`${f}`]).forEach(([h,f])=>{l.searchParams.append(h,f)})):(a="POST",u=n);const d=new Headers(this.headers);return i&&d.set("Prefer",`count=${i}`),new ey.default({method:a,url:l,headers:d,schema:this.schemaName,body:u,fetch:(o=this.fetch)!==null&&o!==void 0?o:fetch})}};Hl.default=ty;var tr=Re&&Re.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(xe,"__esModule",{value:!0});xe.PostgrestError=xe.PostgrestBuilder=xe.PostgrestTransformBuilder=xe.PostgrestFilterBuilder=xe.PostgrestQueryBuilder=xe.PostgrestClient=void 0;const yf=tr(Hl);xe.PostgrestClient=yf.default;const vf=tr(qi);xe.PostgrestQueryBuilder=vf.default;const wf=tr(as);xe.PostgrestFilterBuilder=wf.default;const xf=tr(Ki);xe.PostgrestTransformBuilder=xf.default;const _f=tr(Gi);xe.PostgrestBuilder=_f.default;const kf=tr(Ji);xe.PostgrestError=kf.default;var ny=xe.default={PostgrestClient:yf.default,PostgrestQueryBuilder:vf.default,PostgrestFilterBuilder:wf.default,PostgrestTransformBuilder:xf.default,PostgrestBuilder:_f.default,PostgrestError:kf.default};const{PostgrestClient:ry,PostgrestQueryBuilder:lx,PostgrestFilterBuilder:ux,PostgrestTransformBuilder:cx,PostgrestBuilder:dx,PostgrestError:hx}=ny;class sy{static detectEnvironment(){var t;if(typeof WebSocket<"u")return{type:"native",constructor:WebSocket};if(typeof globalThis<"u"&&typeof globalThis.WebSocket<"u")return{type:"native",constructor:globalThis.WebSocket};if(typeof global<"u"&&typeof global.WebSocket<"u")return{type:"native",constructor:global.WebSocket};if(typeof globalThis<"u"&&typeof globalThis.WebSocketPair<"u"&&typeof globalThis.WebSocket>"u")return{type:"cloudflare",error:"Cloudflare Workers detected. WebSocket clients are not supported in Cloudflare Workers.",workaround:"Use Cloudflare Workers WebSocket API for server-side WebSocket handling, or deploy to a different runtime."};if(typeof globalThis<"u"&&globalThis.EdgeRuntime||typeof navigator<"u"&&(!((t=navigator.userAgent)===null||t===void 0)&&t.includes("Vercel-Edge")))return{type:"unsupported",error:"Edge runtime detected (Vercel Edge/Netlify Edge). WebSockets are not supported in edge functions.",workaround:"Use serverless functions or a different deployment target for WebSocket functionality."};if(typeof process<"u"&&process.versions&&process.versions.node){const n=parseInt(process.versions.node.split(".")[0]);return n>=22?typeof globalThis.WebSocket<"u"?{type:"native",constructor:globalThis.WebSocket}:{type:"unsupported",error:`Node.js ${n} detected but native WebSocket not found.`,workaround:"Provide a WebSocket implementation via the transport option."}:{type:"unsupported",error:`Node.js ${n} detected without native WebSocket support.`,workaround:`For Node.js < 22, install "ws" package and provide it via the transport option:
import ws from "ws"
new RealtimeClient(url, { transport: ws })`}}return{type:"unsupported",error:"Unknown JavaScript runtime without WebSocket support.",workaround:"Ensure you're running in a supported environment (browser, Node.js, Deno) or provide a custom WebSocket implementation."}}static getWebSocketConstructor(){const t=this.detectEnvironment();if(t.constructor)return t.constructor;let n=t.error||"WebSocket not supported in this environment.";throw t.workaround&&(n+=`

Suggested solution: ${t.workaround}`),new Error(n)}static createWebSocket(t,n){const r=this.getWebSocketConstructor();return new r(t,n)}static isWebSocketSupported(){try{const t=this.detectEnvironment();return t.type==="native"||t.type==="ws"}catch{return!1}}}const iy="2.15.4",oy=`realtime-js/${iy}`,ay="1.0.0",$a=1e4,ly=1e3,uy=100;var Nr;(function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"})(Nr||(Nr={}));var te;(function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"})(te||(te={}));var We;(function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"})(We||(We={}));var Oa;(function(e){e.websocket="websocket"})(Oa||(Oa={}));var Qt;(function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"})(Qt||(Qt={}));class cy{constructor(){this.HEADER_LENGTH=1}decode(t,n){return t.constructor===ArrayBuffer?n(this._binaryDecode(t)):n(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){const n=new DataView(t),r=new TextDecoder;return this._decodeBroadcast(t,n,r)}_decodeBroadcast(t,n,r){const s=n.getUint8(1),i=n.getUint8(2);let o=this.HEADER_LENGTH+2;const a=r.decode(t.slice(o,o+s));o=o+s;const l=r.decode(t.slice(o,o+i));o=o+i;const u=JSON.parse(r.decode(t.slice(o,t.byteLength)));return{ref:null,topic:a,event:l,payload:u}}}class Sf{constructor(t,n){this.callback=t,this.timerCalc=n,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=n}reset(){this.tries=0,clearTimeout(this.timer),this.timer=void 0}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var U;(function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"})(U||(U={}));const mc=(e,t,n={})=>{var r;const s=(r=n.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(t).reduce((i,o)=>(i[o]=dy(o,e,t,s),i),{})},dy=(e,t,n,r)=>{const s=t.find(a=>a.name===e),i=s==null?void 0:s.type,o=n[e];return i&&!r.includes(i)?bf(i,o):Ia(o)},bf=(e,t)=>{if(e.charAt(0)==="_"){const n=e.slice(1,e.length);return gy(t,n)}switch(e){case U.bool:return hy(t);case U.float4:case U.float8:case U.int2:case U.int4:case U.int8:case U.numeric:case U.oid:return fy(t);case U.json:case U.jsonb:return py(t);case U.timestamp:return my(t);case U.abstime:case U.date:case U.daterange:case U.int4range:case U.int8range:case U.money:case U.reltime:case U.text:case U.time:case U.timestamptz:case U.timetz:case U.tsrange:case U.tstzrange:return Ia(t);default:return Ia(t)}},Ia=e=>e,hy=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},fy=e=>{if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},py=e=>{if(typeof e=="string")try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},gy=(e,t)=>{if(typeof e!="string")return e;const n=e.length-1,r=e[n];if(e[0]==="{"&&r==="}"){let i;const o=e.slice(1,n);try{i=JSON.parse("["+o+"]")}catch{i=o?o.split(","):[]}return i.map(a=>bf(t,a))}return e},my=e=>typeof e=="string"?e.replace(" ","T"):e,Cf=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")+"/api/broadcast"};class Co{constructor(t,n,r={},s=$a){this.channel=t,this.event=n,this.payload=r,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,n){var r;return this._hasReceived(t)&&n((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:t,callback:n}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const t=n=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=n,this._matchReceive(n)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,n){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:n})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:n}){this.recHooks.filter(r=>r.status===t).forEach(r=>r.callback(n))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var yc;(function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"})(yc||(yc={}));class Tr{constructor(t,n){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.enabled=!1,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(n==null?void 0:n.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},s=>{const{onJoin:i,onLeave:o,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Tr.syncState(this.state,s,i,o),this.pendingDiffs.forEach(l=>{this.state=Tr.syncDiff(this.state,l,i,o)}),this.pendingDiffs=[],a()}),this.channel._on(r.diff,{},s=>{const{onJoin:i,onLeave:o,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(s):(this.state=Tr.syncDiff(this.state,s,i,o),a())}),this.onJoin((s,i,o)=>{this.channel._trigger("presence",{event:"join",key:s,currentPresences:i,newPresences:o})}),this.onLeave((s,i,o)=>{this.channel._trigger("presence",{event:"leave",key:s,currentPresences:i,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,n,r,s){const i=this.cloneDeep(t),o=this.transformState(n),a={},l={};return this.map(i,(u,d)=>{o[u]||(l[u]=d)}),this.map(o,(u,d)=>{const h=i[u];if(h){const f=d.map(_=>_.presence_ref),y=h.map(_=>_.presence_ref),v=d.filter(_=>y.indexOf(_.presence_ref)<0),x=h.filter(_=>f.indexOf(_.presence_ref)<0);v.length>0&&(a[u]=v),x.length>0&&(l[u]=x)}else a[u]=d}),this.syncDiff(i,{joins:a,leaves:l},r,s)}static syncDiff(t,n,r,s){const{joins:i,leaves:o}={joins:this.transformState(n.joins),leaves:this.transformState(n.leaves)};return r||(r=()=>{}),s||(s=()=>{}),this.map(i,(a,l)=>{var u;const d=(u=t[a])!==null&&u!==void 0?u:[];if(t[a]=this.cloneDeep(l),d.length>0){const h=t[a].map(y=>y.presence_ref),f=d.filter(y=>h.indexOf(y.presence_ref)<0);t[a].unshift(...f)}r(a,d,l)}),this.map(o,(a,l)=>{let u=t[a];if(!u)return;const d=l.map(h=>h.presence_ref);u=u.filter(h=>d.indexOf(h.presence_ref)<0),t[a]=u,s(a,u,l),u.length===0&&delete t[a]}),t}static map(t,n){return Object.getOwnPropertyNames(t).map(r=>n(r,t[r]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((n,r)=>{const s=t[r];return"metas"in s?n[r]=s.metas.map(i=>(i.presence_ref=i.phx_ref,delete i.phx_ref,delete i.phx_ref_prev,i)):n[r]=s,n},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var vc;(function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"})(vc||(vc={}));var Ar;(function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"})(Ar||(Ar={}));var it;(function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"})(it||(it={}));class Vl{constructor(t,n={config:{}},r){this.topic=t,this.params=n,this.socket=r,this.bindings={},this.state=te.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:"",enabled:!1},private:!1},n.config),this.timeout=this.socket.timeout,this.joinPush=new Co(this,We.join,this.params,this.timeout),this.rejoinTimer=new Sf(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=te.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(s=>s.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=te.closed,this.socket._remove(this)}),this._onError(s=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,s),this.state=te.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=te.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("error",s=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,s),this.state=te.errored,this.rejoinTimer.scheduleTimeout())}),this._on(We.reply,{},(s,i)=>{this._trigger(this._replyEventName(i),s)}),this.presence=new Tr(this),this.broadcastEndpointURL=Cf(this.socket.endPoint),this.private=this.params.config.private||!1}subscribe(t,n=this.timeout){var r,s,i;if(this.socket.isConnected()||this.socket.connect(),this.state==te.closed){const{config:{broadcast:o,presence:a,private:l}}=this.params,u=(s=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(y=>y.filter))!==null&&s!==void 0?s:[],d=!!this.bindings[Ar.PRESENCE]&&this.bindings[Ar.PRESENCE].length>0||((i=this.params.config.presence)===null||i===void 0?void 0:i.enabled)===!0,h={},f={broadcast:o,presence:Object.assign(Object.assign({},a),{enabled:d}),postgres_changes:u,private:l};this.socket.accessTokenValue&&(h.access_token=this.socket.accessTokenValue),this._onError(y=>t==null?void 0:t(it.CHANNEL_ERROR,y)),this._onClose(()=>t==null?void 0:t(it.CLOSED)),this.updateJoinPayload(Object.assign({config:f},h)),this.joinedOnce=!0,this._rejoin(n),this.joinPush.receive("ok",async({postgres_changes:y})=>{var v;if(this.socket.setAuth(),y===void 0){t==null||t(it.SUBSCRIBED);return}else{const x=this.bindings.postgres_changes,_=(v=x==null?void 0:x.length)!==null&&v!==void 0?v:0,m=[];for(let p=0;p<_;p++){const g=x[p],{filter:{event:w,schema:S,table:b,filter:P}}=g,M=y&&y[p];if(M&&M.event===w&&M.schema===S&&M.table===b&&M.filter===P)m.push(Object.assign(Object.assign({},g),{id:M.id}));else{this.unsubscribe(),this.state=te.errored,t==null||t(it.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=m,t&&t(it.SUBSCRIBED);return}}).receive("error",y=>{this.state=te.errored,t==null||t(it.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(y).join(", ")||"error")))}).receive("timeout",()=>{t==null||t(it.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(t,n={}){return await this.send({type:"presence",event:"track",payload:t},n.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,n,r){return this.state===te.joined&&t===Ar.PRESENCE&&(this.socket.log("channel",`resubscribe to ${this.topic} due to change in presence callbacks on joined channel`),this.unsubscribe().then(()=>this.subscribe())),this._on(t,n,r)}async send(t,n={}){var r,s;if(!this._canPush()&&t.type==="broadcast"){const{event:i,payload:o}=t,l={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:o,private:this.private}]})};try{const u=await this._fetchWithTimeout(this.broadcastEndpointURL,l,(r=n.timeout)!==null&&r!==void 0?r:this.timeout);return await((s=u.body)===null||s===void 0?void 0:s.cancel()),u.ok?"ok":"error"}catch(u){return u.name==="AbortError"?"timed out":"error"}}else return new Promise(i=>{var o,a,l;const u=this._push(t.type,t,n.timeout||this.timeout);t.type==="broadcast"&&!(!((l=(a=(o=this.params)===null||o===void 0?void 0:o.config)===null||a===void 0?void 0:a.broadcast)===null||l===void 0)&&l.ack)&&i("ok"),u.receive("ok",()=>i("ok")),u.receive("error",()=>i("error")),u.receive("timeout",()=>i("timed out"))})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=te.leaving;const n=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(We.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(s=>{r=new Co(this,We.leave,{},t),r.receive("ok",()=>{n(),s("ok")}).receive("timeout",()=>{n(),s("timed out")}).receive("error",()=>{s("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{r==null||r.destroy()})}teardown(){this.pushBuffer.forEach(t=>t.destroy()),this.pushBuffer=[],this.rejoinTimer.reset(),this.joinPush.destroy(),this.state=te.closed,this.bindings={}}async _fetchWithTimeout(t,n,r){const s=new AbortController,i=setTimeout(()=>s.abort(),r),o=await this.socket.fetch(t,Object.assign(Object.assign({},n),{signal:s.signal}));return clearTimeout(i),o}_push(t,n,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new Co(this,t,n,r);return this._canPush()?s.send():this._addToPushBuffer(s),s}_addToPushBuffer(t){if(t.startTimeout(),this.pushBuffer.push(t),this.pushBuffer.length>uy){const n=this.pushBuffer.shift();n&&(n.destroy(),this.socket.log("channel",`discarded push due to buffer overflow: ${n.event}`,n.payload))}}_onMessage(t,n,r){return n}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,n,r){var s,i;const o=t.toLocaleLowerCase(),{close:a,error:l,leave:u,join:d}=We;if(r&&[a,l,u,d].indexOf(o)>=0&&r!==this._joinRef())return;let f=this._onMessage(o,n,r);if(n&&!f)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(s=this.bindings.postgres_changes)===null||s===void 0||s.filter(y=>{var v,x,_;return((v=y.filter)===null||v===void 0?void 0:v.event)==="*"||((_=(x=y.filter)===null||x===void 0?void 0:x.event)===null||_===void 0?void 0:_.toLocaleLowerCase())===o}).map(y=>y.callback(f,r)):(i=this.bindings[o])===null||i===void 0||i.filter(y=>{var v,x,_,m,p,g;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in y){const w=y.id,S=(v=y.filter)===null||v===void 0?void 0:v.event;return w&&((x=n.ids)===null||x===void 0?void 0:x.includes(w))&&(S==="*"||(S==null?void 0:S.toLocaleLowerCase())===((_=n.data)===null||_===void 0?void 0:_.type.toLocaleLowerCase()))}else{const w=(p=(m=y==null?void 0:y.filter)===null||m===void 0?void 0:m.event)===null||p===void 0?void 0:p.toLocaleLowerCase();return w==="*"||w===((g=n==null?void 0:n.event)===null||g===void 0?void 0:g.toLocaleLowerCase())}else return y.type.toLocaleLowerCase()===o}).map(y=>{if(typeof f=="object"&&"ids"in f){const v=f.data,{schema:x,table:_,commit_timestamp:m,type:p,errors:g}=v;f=Object.assign(Object.assign({},{schema:x,table:_,commit_timestamp:m,eventType:p,new:{},old:{},errors:g}),this._getPayloadRecords(v))}y.callback(f,r)})}_isClosed(){return this.state===te.closed}_isJoined(){return this.state===te.joined}_isJoining(){return this.state===te.joining}_isLeaving(){return this.state===te.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,n,r){const s=t.toLocaleLowerCase(),i={type:s,filter:n,callback:r};return this.bindings[s]?this.bindings[s].push(i):this.bindings[s]=[i],this}_off(t,n){const r=t.toLocaleLowerCase();return this.bindings[r]&&(this.bindings[r]=this.bindings[r].filter(s=>{var i;return!(((i=s.type)===null||i===void 0?void 0:i.toLocaleLowerCase())===r&&Vl.isEqual(s.filter,n))})),this}static isEqual(t,n){if(Object.keys(t).length!==Object.keys(n).length)return!1;for(const r in t)if(t[r]!==n[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(We.close,{},t)}_onError(t){this._on(We.error,{},n=>t(n))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=te.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const n={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(n.new=mc(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(n.old=mc(t.columns,t.old_record)),n}}const Po=()=>{},Ms={HEARTBEAT_INTERVAL:25e3,RECONNECT_DELAY:10,HEARTBEAT_TIMEOUT_FALLBACK:100},yy=[1e3,2e3,5e3,1e4],vy=1e4,wy=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class xy{constructor(t,n){var r;if(this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=$a,this.transport=null,this.heartbeatIntervalMs=Ms.HEARTBEAT_INTERVAL,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Po,this.ref=0,this.reconnectTimer=null,this.logger=Po,this.conn=null,this.sendBuffer=[],this.serializer=new cy,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._connectionState="disconnected",this._wasManualDisconnect=!1,this._authPromise=null,this._resolveFetch=s=>{let i;return s?i=s:typeof fetch>"u"?i=(...o)=>os(async()=>{const{default:a}=await Promise.resolve().then(()=>er);return{default:a}},void 0).then(({default:a})=>a(...o)).catch(a=>{throw new Error(`Failed to load @supabase/node-fetch: ${a.message}. This is required for HTTP requests in Node.js environments without native fetch.`)}):i=fetch,(...o)=>i(...o)},!(!((r=n==null?void 0:n.params)===null||r===void 0)&&r.apikey))throw new Error("API key is required to connect to Realtime");this.apiKey=n.params.apikey,this.endPoint=`${t}/${Oa.websocket}`,this.httpEndpoint=Cf(t),this._initializeOptions(n),this._setupReconnectionTimer(),this.fetch=this._resolveFetch(n==null?void 0:n.fetch)}connect(){if(!(this.isConnecting()||this.isDisconnecting()||this.conn!==null&&this.isConnected())){if(this._setConnectionState("connecting"),this._setAuthSafely("connect"),this.transport)this.conn=new this.transport(this.endpointURL());else try{this.conn=sy.createWebSocket(this.endpointURL())}catch(t){this._setConnectionState("disconnected");const n=t.message;throw n.includes("Node.js")?new Error(`${n}

To use Realtime in Node.js, you need to provide a WebSocket implementation:

Option 1: Use Node.js 22+ which has native WebSocket support
Option 2: Install and provide the "ws" package:

  npm install ws

  import ws from "ws"
  const client = new RealtimeClient(url, {
    ...options,
    transport: ws
  })`):new Error(`WebSocket not available: ${n}`)}this._setupConnectionHandlers()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:ay}))}disconnect(t,n){if(!this.isDisconnecting())if(this._setConnectionState("disconnecting",!0),this.conn){const r=setTimeout(()=>{this._setConnectionState("disconnected")},100);this.conn.onclose=()=>{clearTimeout(r),this._setConnectionState("disconnected")},t?this.conn.close(t,n??""):this.conn.close(),this._teardownConnection()}else this._setConnectionState("disconnected")}getChannels(){return this.channels}async removeChannel(t){const n=await t.unsubscribe();return this.channels.length===0&&this.disconnect(),n}async removeAllChannels(){const t=await Promise.all(this.channels.map(n=>n.unsubscribe()));return this.channels=[],this.disconnect(),t}log(t,n,r){this.logger(t,n,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Nr.connecting:return Qt.Connecting;case Nr.open:return Qt.Open;case Nr.closing:return Qt.Closing;default:return Qt.Closed}}isConnected(){return this.connectionState()===Qt.Open}isConnecting(){return this._connectionState==="connecting"}isDisconnecting(){return this._connectionState==="disconnecting"}channel(t,n={config:{}}){const r=`realtime:${t}`,s=this.getChannels().find(i=>i.topic===r);if(s)return s;{const i=new Vl(`realtime:${t}`,n,this);return this.channels.push(i),i}}push(t){const{topic:n,event:r,payload:s,ref:i}=t,o=()=>{this.encode(t,a=>{var l;(l=this.conn)===null||l===void 0||l.send(a)})};this.log("push",`${n} ${r} (${i})`,s),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(t=null){this._authPromise=this._performAuth(t);try{await this._authPromise}finally{this._authPromise=null}}async sendHeartbeat(){var t;if(!this.isConnected()){try{this.heartbeatCallback("disconnected")}catch(n){this.log("error","error in heartbeat callback",n)}return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection");try{this.heartbeatCallback("timeout")}catch(n){this.log("error","error in heartbeat callback",n)}this._wasManualDisconnect=!1,(t=this.conn)===null||t===void 0||t.close(ly,"heartbeat timeout"),setTimeout(()=>{var n;this.isConnected()||(n=this.reconnectTimer)===null||n===void 0||n.scheduleTimeout()},Ms.HEARTBEAT_TIMEOUT_FALLBACK);return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef});try{this.heartbeatCallback("sent")}catch(n){this.log("error","error in heartbeat callback",n)}this._setAuthSafely("heartbeat")}onHeartbeat(t){this.heartbeatCallback=t}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let n=this.channels.find(r=>r.topic===t&&(r._isJoined()||r._isJoining()));n&&(this.log("transport",`leaving duplicate topic "${t}"`),n.unsubscribe())}_remove(t){this.channels=this.channels.filter(n=>n.topic!==t.topic)}_onConnMessage(t){this.decode(t.data,n=>{if(n.topic==="phoenix"&&n.event==="phx_reply")try{this.heartbeatCallback(n.payload.status==="ok"?"ok":"error")}catch(u){this.log("error","error in heartbeat callback",u)}n.ref&&n.ref===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null);const{topic:r,event:s,payload:i,ref:o}=n,a=o?`(${o})`:"",l=i.status||"";this.log("receive",`${l} ${r} ${s} ${a}`.trim(),i),this.channels.filter(u=>u._isMember(r)).forEach(u=>u._trigger(s,i,o)),this._triggerStateCallbacks("message",n)})}_clearTimer(t){var n;t==="heartbeat"&&this.heartbeatTimer?(clearInterval(this.heartbeatTimer),this.heartbeatTimer=void 0):t==="reconnect"&&((n=this.reconnectTimer)===null||n===void 0||n.reset())}_clearAllTimers(){this._clearTimer("heartbeat"),this._clearTimer("reconnect")}_setupConnectionHandlers(){this.conn&&("binaryType"in this.conn&&(this.conn.binaryType="arraybuffer"),this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_teardownConnection(){this.conn&&(this.conn.onopen=null,this.conn.onerror=null,this.conn.onmessage=null,this.conn.onclose=null,this.conn=null),this._clearAllTimers(),this.channels.forEach(t=>t.teardown())}_onConnOpen(){this._setConnectionState("connected"),this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this._clearTimer("reconnect"),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this._triggerStateCallbacks("open")}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=n=>{this.log("worker","worker error",n.message),this.workerRef.terminate()},this.workerRef.onmessage=n=>{n.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(t){var n;this._setConnectionState("disconnected"),this.log("transport","close",t),this._triggerChanError(),this._clearTimer("heartbeat"),this._wasManualDisconnect||(n=this.reconnectTimer)===null||n===void 0||n.scheduleTimeout(),this._triggerStateCallbacks("close",t)}_onConnError(t){this._setConnectionState("disconnected"),this.log("transport",`${t}`),this._triggerChanError(),this._triggerStateCallbacks("error",t)}_triggerChanError(){this.channels.forEach(t=>t._trigger(We.error))}_appendParams(t,n){if(Object.keys(n).length===0)return t;const r=t.match(/\?/)?"&":"?",s=new URLSearchParams(n);return`${t}${r}${s}`}_workerObjectUrl(t){let n;if(t)n=t;else{const r=new Blob([wy],{type:"application/javascript"});n=URL.createObjectURL(r)}return n}_setConnectionState(t,n=!1){this._connectionState=t,t==="connecting"?this._wasManualDisconnect=!1:t==="disconnecting"&&(this._wasManualDisconnect=n)}async _performAuth(t=null){let n;t?n=t:this.accessToken?n=await this.accessToken():n=this.accessTokenValue,this.accessTokenValue!=n&&(this.accessTokenValue=n,this.channels.forEach(r=>{const s={access_token:n,version:oy};n&&r.updateJoinPayload(s),r.joinedOnce&&r._isJoined()&&r._push(We.access_token,{access_token:n})}))}async _waitForAuthIfNeeded(){this._authPromise&&await this._authPromise}_setAuthSafely(t="general"){this.setAuth().catch(n=>{this.log("error",`error setting auth in ${t}`,n)})}_triggerStateCallbacks(t,n){try{this.stateChangeCallbacks[t].forEach(r=>{try{r(n)}catch(s){this.log("error",`error in ${t} callback`,s)}})}catch(r){this.log("error",`error triggering ${t} callbacks`,r)}}_setupReconnectionTimer(){this.reconnectTimer=new Sf(async()=>{setTimeout(async()=>{await this._waitForAuthIfNeeded(),this.isConnected()||this.connect()},Ms.RECONNECT_DELAY)},this.reconnectAfterMs)}_initializeOptions(t){var n,r,s,i,o,a,l,u,d;if(this.transport=(n=t==null?void 0:t.transport)!==null&&n!==void 0?n:null,this.timeout=(r=t==null?void 0:t.timeout)!==null&&r!==void 0?r:$a,this.heartbeatIntervalMs=(s=t==null?void 0:t.heartbeatIntervalMs)!==null&&s!==void 0?s:Ms.HEARTBEAT_INTERVAL,this.worker=(i=t==null?void 0:t.worker)!==null&&i!==void 0?i:!1,this.accessToken=(o=t==null?void 0:t.accessToken)!==null&&o!==void 0?o:null,this.heartbeatCallback=(a=t==null?void 0:t.heartbeatCallback)!==null&&a!==void 0?a:Po,t!=null&&t.params&&(this.params=t.params),t!=null&&t.logger&&(this.logger=t.logger),(t!=null&&t.logLevel||t!=null&&t.log_level)&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),this.reconnectAfterMs=(l=t==null?void 0:t.reconnectAfterMs)!==null&&l!==void 0?l:h=>yy[h-1]||vy,this.encode=(u=t==null?void 0:t.encode)!==null&&u!==void 0?u:(h,f)=>f(JSON.stringify(h)),this.decode=(d=t==null?void 0:t.decode)!==null&&d!==void 0?d:this.serializer.decode.bind(this.serializer),this.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.workerUrl=t==null?void 0:t.workerUrl}}}class ql extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function ne(e){return typeof e=="object"&&e!==null&&"__isStorageError"in e}class _y extends ql{constructor(t,n,r){super(t),this.name="StorageApiError",this.status=n,this.statusCode=r}toJSON(){return{name:this.name,message:this.message,status:this.status,statusCode:this.statusCode}}}class Ra extends ql{constructor(t,n){super(t),this.name="StorageUnknownError",this.originalError=n}}var ky=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function a(d){try{u(r.next(d))}catch(h){o(h)}}function l(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const Pf=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>os(async()=>{const{default:r}=await Promise.resolve().then(()=>er);return{default:r}},void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},Sy=()=>ky(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield os(()=>Promise.resolve().then(()=>er),void 0)).Response:Response}),La=e=>{if(Array.isArray(e))return e.map(n=>La(n));if(typeof e=="function"||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([n,r])=>{const s=n.replace(/([-_][a-z])/gi,i=>i.toUpperCase().replace(/[-_]/g,""));t[s]=La(r)}),t},by=e=>{if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)};var hn=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function a(d){try{u(r.next(d))}catch(h){o(h)}}function l(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const jo=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Cy=(e,t,n)=>hn(void 0,void 0,void 0,function*(){const r=yield Sy();e instanceof r&&!(n!=null&&n.noResolveJson)?e.json().then(s=>{const i=e.status||500,o=(s==null?void 0:s.statusCode)||i+"";t(new _y(jo(s),i,o))}).catch(s=>{t(new Ra(jo(s),s))}):t(new Ra(jo(e),e))}),Py=(e,t,n,r)=>{const s={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"||!r?s:(by(r)?(s.headers=Object.assign({"Content-Type":"application/json"},t==null?void 0:t.headers),s.body=JSON.stringify(r)):s.body=r,t!=null&&t.duplex&&(s.duplex=t.duplex),Object.assign(Object.assign({},s),n))};function ls(e,t,n,r,s,i){return hn(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(n,Py(t,r,s,i)).then(l=>{if(!l.ok)throw l;return r!=null&&r.noResolveJson?l:l.json()}).then(l=>o(l)).catch(l=>Cy(l,a,r))})})}function bi(e,t,n,r){return hn(this,void 0,void 0,function*(){return ls(e,"GET",t,n,r)})}function Ye(e,t,n,r,s){return hn(this,void 0,void 0,function*(){return ls(e,"POST",t,r,s,n)})}function Da(e,t,n,r,s){return hn(this,void 0,void 0,function*(){return ls(e,"PUT",t,r,s,n)})}function jy(e,t,n,r){return hn(this,void 0,void 0,function*(){return ls(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)})}function jf(e,t,n,r,s){return hn(this,void 0,void 0,function*(){return ls(e,"DELETE",t,r,s,n)})}var pe=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function a(d){try{u(r.next(d))}catch(h){o(h)}}function l(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const Ey={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},wc={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class My{constructor(t,n={},r,s){this.url=t,this.headers=n,this.bucketId=r,this.fetch=Pf(s)}uploadOrUpdate(t,n,r,s){return pe(this,void 0,void 0,function*(){try{let i;const o=Object.assign(Object.assign({},wc),s);let a=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(o.upsert)});const l=o.metadata;typeof Blob<"u"&&r instanceof Blob?(i=new FormData,i.append("cacheControl",o.cacheControl),l&&i.append("metadata",this.encodeMetadata(l)),i.append("",r)):typeof FormData<"u"&&r instanceof FormData?(i=r,i.append("cacheControl",o.cacheControl),l&&i.append("metadata",this.encodeMetadata(l))):(i=r,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType,l&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),s!=null&&s.headers&&(a=Object.assign(Object.assign({},a),s.headers));const u=this._removeEmptyFolders(n),d=this._getFinalPath(u),h=yield(t=="PUT"?Da:Ye)(this.fetch,`${this.url}/object/${d}`,i,Object.assign({headers:a},o!=null&&o.duplex?{duplex:o.duplex}:{}));return{data:{path:u,id:h.Id,fullPath:h.Key},error:null}}catch(i){if(ne(i))return{data:null,error:i};throw i}})}upload(t,n,r){return pe(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,n,r)})}uploadToSignedUrl(t,n,r,s){return pe(this,void 0,void 0,function*(){const i=this._removeEmptyFolders(t),o=this._getFinalPath(i),a=new URL(this.url+`/object/upload/sign/${o}`);a.searchParams.set("token",n);try{let l;const u=Object.assign({upsert:wc.upsert},s),d=Object.assign(Object.assign({},this.headers),{"x-upsert":String(u.upsert)});typeof Blob<"u"&&r instanceof Blob?(l=new FormData,l.append("cacheControl",u.cacheControl),l.append("",r)):typeof FormData<"u"&&r instanceof FormData?(l=r,l.append("cacheControl",u.cacheControl)):(l=r,d["cache-control"]=`max-age=${u.cacheControl}`,d["content-type"]=u.contentType);const h=yield Da(this.fetch,a.toString(),l,{headers:d});return{data:{path:i,fullPath:h.Key},error:null}}catch(l){if(ne(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(t,n){return pe(this,void 0,void 0,function*(){try{let r=this._getFinalPath(t);const s=Object.assign({},this.headers);n!=null&&n.upsert&&(s["x-upsert"]="true");const i=yield Ye(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:s}),o=new URL(this.url+i.url),a=o.searchParams.get("token");if(!a)throw new ql("No token returned by API");return{data:{signedUrl:o.toString(),path:t,token:a},error:null}}catch(r){if(ne(r))return{data:null,error:r};throw r}})}update(t,n,r){return pe(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,n,r)})}move(t,n,r){return pe(this,void 0,void 0,function*(){try{return{data:yield Ye(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(s){if(ne(s))return{data:null,error:s};throw s}})}copy(t,n,r){return pe(this,void 0,void 0,function*(){try{return{data:{path:(yield Ye(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(s){if(ne(s))return{data:null,error:s};throw s}})}createSignedUrl(t,n,r){return pe(this,void 0,void 0,function*(){try{let s=this._getFinalPath(t),i=yield Ye(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:n},r!=null&&r.transform?{transform:r.transform}:{}),{headers:this.headers});const o=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${o}`)},{data:i,error:null}}catch(s){if(ne(s))return{data:null,error:s};throw s}})}createSignedUrls(t,n,r){return pe(this,void 0,void 0,function*(){try{const s=yield Ye(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:n,paths:t},{headers:this.headers}),i=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return{data:s.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${i}`):null})),error:null}}catch(s){if(ne(s))return{data:null,error:s};throw s}})}download(t,n){return pe(this,void 0,void 0,function*(){const s=typeof(n==null?void 0:n.transform)<"u"?"render/image/authenticated":"object",i=this.transformOptsToQueryString((n==null?void 0:n.transform)||{}),o=i?`?${i}`:"";try{const a=this._getFinalPath(t);return{data:yield(yield bi(this.fetch,`${this.url}/${s}/${a}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(ne(a))return{data:null,error:a};throw a}})}info(t){return pe(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{const r=yield bi(this.fetch,`${this.url}/object/info/${n}`,{headers:this.headers});return{data:La(r),error:null}}catch(r){if(ne(r))return{data:null,error:r};throw r}})}exists(t){return pe(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{return yield jy(this.fetch,`${this.url}/object/${n}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(ne(r)&&r instanceof Ra){const s=r.originalError;if([400,404].includes(s==null?void 0:s.status))return{data:!1,error:r}}throw r}})}getPublicUrl(t,n){const r=this._getFinalPath(t),s=[],i=n!=null&&n.download?`download=${n.download===!0?"":n.download}`:"";i!==""&&s.push(i);const a=typeof(n==null?void 0:n.transform)<"u"?"render/image":"object",l=this.transformOptsToQueryString((n==null?void 0:n.transform)||{});l!==""&&s.push(l);let u=s.join("&");return u!==""&&(u=`?${u}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${r}${u}`)}}}remove(t){return pe(this,void 0,void 0,function*(){try{return{data:yield jf(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(n){if(ne(n))return{data:null,error:n};throw n}})}list(t,n,r){return pe(this,void 0,void 0,function*(){try{const s=Object.assign(Object.assign(Object.assign({},Ey),n),{prefix:t||""});return{data:yield Ye(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},r),error:null}}catch(s){if(ne(s))return{data:null,error:s};throw s}})}listV2(t,n){return pe(this,void 0,void 0,function*(){try{const r=Object.assign({},t);return{data:yield Ye(this.fetch,`${this.url}/object/list-v2/${this.bucketId}`,r,{headers:this.headers},n),error:null}}catch(r){if(ne(r))return{data:null,error:r};throw r}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t.replace(/^\/+/,"")}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const n=[];return t.width&&n.push(`width=${t.width}`),t.height&&n.push(`height=${t.height}`),t.resize&&n.push(`resize=${t.resize}`),t.format&&n.push(`format=${t.format}`),t.quality&&n.push(`quality=${t.quality}`),n.join("&")}}const Ny="2.11.0",Ty={"X-Client-Info":`storage-js/${Ny}`};var mn=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function a(d){try{u(r.next(d))}catch(h){o(h)}}function l(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class Ay{constructor(t,n={},r,s){const i=new URL(t);s!=null&&s.useNewHostname&&/supabase\.(co|in|red)$/.test(i.hostname)&&!i.hostname.includes("storage.supabase.")&&(i.hostname=i.hostname.replace("supabase.","storage.supabase.")),this.url=i.href,this.headers=Object.assign(Object.assign({},Ty),n),this.fetch=Pf(r)}listBuckets(){return mn(this,void 0,void 0,function*(){try{return{data:yield bi(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(ne(t))return{data:null,error:t};throw t}})}getBucket(t){return mn(this,void 0,void 0,function*(){try{return{data:yield bi(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(n){if(ne(n))return{data:null,error:n};throw n}})}createBucket(t,n={public:!1}){return mn(this,void 0,void 0,function*(){try{return{data:yield Ye(this.fetch,`${this.url}/bucket`,{id:t,name:t,type:n.type,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(ne(r))return{data:null,error:r};throw r}})}updateBucket(t,n){return mn(this,void 0,void 0,function*(){try{return{data:yield Da(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(ne(r))return{data:null,error:r};throw r}})}emptyBucket(t){return mn(this,void 0,void 0,function*(){try{return{data:yield Ye(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(n){if(ne(n))return{data:null,error:n};throw n}})}deleteBucket(t){return mn(this,void 0,void 0,function*(){try{return{data:yield jf(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(n){if(ne(n))return{data:null,error:n};throw n}})}}class $y extends Ay{constructor(t,n={},r,s){super(t,n,r,s)}from(t){return new My(this.url,this.headers,t,this.fetch)}}const Oy="2.56.1";let wr="";typeof Deno<"u"?wr="deno":typeof document<"u"?wr="web":typeof navigator<"u"&&navigator.product==="ReactNative"?wr="react-native":wr="node";const Iy={"X-Client-Info":`supabase-js-${wr}/${Oy}`},Ry={headers:Iy},Ly={schema:"public"},Dy={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},Fy={};var zy=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function a(d){try{u(r.next(d))}catch(h){o(h)}}function l(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const Uy=e=>{let t;return e?t=e:typeof fetch>"u"?t=hf:t=fetch,(...n)=>t(...n)},By=()=>typeof Headers>"u"?ff:Headers,Wy=(e,t,n)=>{const r=Uy(n),s=By();return(i,o)=>zy(void 0,void 0,void 0,function*(){var a;const l=(a=yield t())!==null&&a!==void 0?a:e;let u=new s(o==null?void 0:o.headers);return u.has("apikey")||u.set("apikey",e),u.has("Authorization")||u.set("Authorization",`Bearer ${l}`),r(i,Object.assign(Object.assign({},o),{headers:u}))})};var Hy=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function a(d){try{u(r.next(d))}catch(h){o(h)}}function l(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};function Vy(e){return e.endsWith("/")?e:e+"/"}function qy(e,t){var n,r;const{db:s,auth:i,realtime:o,global:a}=e,{db:l,auth:u,realtime:d,global:h}=t,f={db:Object.assign(Object.assign({},l),s),auth:Object.assign(Object.assign({},u),i),realtime:Object.assign(Object.assign({},d),o),storage:{},global:Object.assign(Object.assign(Object.assign({},h),a),{headers:Object.assign(Object.assign({},(n=h==null?void 0:h.headers)!==null&&n!==void 0?n:{}),(r=a==null?void 0:a.headers)!==null&&r!==void 0?r:{})}),accessToken:()=>Hy(this,void 0,void 0,function*(){return""})};return e.accessToken?f.accessToken=e.accessToken:delete f.accessToken,f}const Ef="2.71.1",kn=30*1e3,Fa=3,Eo=Fa*kn,Ky="http://localhost:9999",Gy="supabase.auth.token",Jy={"X-Client-Info":`gotrue-js/${Ef}`},za="X-Supabase-Api-Version",Mf={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},Qy=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,Xy=10*60*1e3;class Kl extends Error{constructor(t,n,r){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=n,this.code=r}}function I(e){return typeof e=="object"&&e!==null&&"__isAuthError"in e}class Yy extends Kl{constructor(t,n,r){super(t,n,r),this.name="AuthApiError",this.status=n,this.code=r}}function Zy(e){return I(e)&&e.name==="AuthApiError"}class Nf extends Kl{constructor(t,n){super(t),this.name="AuthUnknownError",this.originalError=n}}class Ut extends Kl{constructor(t,n,r,s){super(t,r,s),this.name=n,this.status=r}}class vt extends Ut{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function ev(e){return I(e)&&e.name==="AuthSessionMissingError"}class Ns extends Ut{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Ts extends Ut{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class As extends Ut{constructor(t,n=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function tv(e){return I(e)&&e.name==="AuthImplicitGrantRedirectError"}class xc extends Ut{constructor(t,n=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Ua extends Ut{constructor(t,n){super(t,"AuthRetryableFetchError",n,void 0)}}function Mo(e){return I(e)&&e.name==="AuthRetryableFetchError"}class _c extends Ut{constructor(t,n,r){super(t,"AuthWeakPasswordError",n,"weak_password"),this.reasons=r}}class Ba extends Ut{constructor(t){super(t,"AuthInvalidJwtError",400,"invalid_jwt")}}const Ci="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),kc=` 	
\r=`.split(""),nv=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<kc.length;t+=1)e[kc[t].charCodeAt(0)]=-2;for(let t=0;t<Ci.length;t+=1)e[Ci[t].charCodeAt(0)]=t;return e})();function Sc(e,t,n){if(e!==null)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(Ci[r]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(Ci[r]),t.queuedBits-=6}}function Tf(e,t,n){const r=nv[e];if(r>-1)for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)n(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else{if(r===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}}function bc(e){const t=[],n=o=>{t.push(String.fromCodePoint(o))},r={utf8seq:0,codepoint:0},s={queue:0,queuedBits:0},i=o=>{iv(o,r,n)};for(let o=0;o<e.length;o+=1)Tf(e.charCodeAt(o),s,i);return t.join("")}function rv(e,t){if(e<=127){t(e);return}else if(e<=2047){t(192|e>>6),t(128|e&63);return}else if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|e&63);return}else if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|e&63);return}throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function sv(e,t){for(let n=0;n<e.length;n+=1){let r=e.charCodeAt(n);if(r>55295&&r<=56319){const s=(r-55296)*1024&65535;r=(e.charCodeAt(n+1)-56320&65535|s)+65536,n+=1}rv(r,t)}}function iv(e,t,n){if(t.utf8seq===0){if(e<=127){n(e);return}for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(t.utf8seq===2)t.codepoint=e&31;else if(t.utf8seq===3)t.codepoint=e&15;else if(t.utf8seq===4)t.codepoint=e&7;else throw new Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|e&63,t.utf8seq-=1,t.utf8seq===0&&n(t.codepoint)}}function ov(e){const t=[],n={queue:0,queuedBits:0},r=s=>{t.push(s)};for(let s=0;s<e.length;s+=1)Tf(e.charCodeAt(s),n,r);return new Uint8Array(t)}function av(e){const t=[];return sv(e,n=>t.push(n)),new Uint8Array(t)}function lv(e){const t=[],n={queue:0,queuedBits:0},r=s=>{t.push(s)};return e.forEach(s=>Sc(s,n,r)),Sc(null,n,r),t.join("")}function uv(e){return Math.round(Date.now()/1e3)+e}function cv(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e=="x"?t:t&3|8).toString(16)})}const ze=()=>typeof window<"u"&&typeof document<"u",Vt={tested:!1,writable:!1},Af=()=>{if(!ze())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(Vt.tested)return Vt.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),Vt.tested=!0,Vt.writable=!0}catch{Vt.tested=!0,Vt.writable=!1}return Vt.writable};function dv(e){const t={},n=new URL(e);if(n.hash&&n.hash[0]==="#")try{new URLSearchParams(n.hash.substring(1)).forEach((s,i)=>{t[i]=s})}catch{}return n.searchParams.forEach((r,s)=>{t[s]=r}),t}const $f=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>os(async()=>{const{default:r}=await Promise.resolve().then(()=>er);return{default:r}},void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},hv=e=>typeof e=="object"&&e!==null&&"status"in e&&"ok"in e&&"json"in e&&typeof e.json=="function",Sn=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},qt=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch{return n}},yt=async(e,t)=>{await e.removeItem(t)};class Qi{constructor(){this.promise=new Qi.promiseConstructor((t,n)=>{this.resolve=t,this.reject=n})}}Qi.promiseConstructor=Promise;function No(e){const t=e.split(".");if(t.length!==3)throw new Ba("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!Qy.test(t[r]))throw new Ba("JWT not in base64url format");return{header:JSON.parse(bc(t[0])),payload:JSON.parse(bc(t[1])),signature:ov(t[2]),raw:{header:t[0],payload:t[1]}}}async function fv(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function pv(e,t){return new Promise((r,s)=>{(async()=>{for(let i=0;i<1/0;i++)try{const o=await e(i);if(!t(i,null,o)){r(o);return}}catch(o){if(!t(i,o)){s(o);return}}})()})}function gv(e){return("0"+e.toString(16)).substr(-2)}function mv(){const t=new Uint32Array(56);if(typeof crypto>"u"){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=n.length;let s="";for(let i=0;i<56;i++)s+=n.charAt(Math.floor(Math.random()*r));return s}return crypto.getRandomValues(t),Array.from(t,gv).join("")}async function yv(e){const n=new TextEncoder().encode(e),r=await crypto.subtle.digest("SHA-256",n),s=new Uint8Array(r);return Array.from(s).map(i=>String.fromCharCode(i)).join("")}async function vv(e){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const n=await yv(e);return btoa(n).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function yn(e,t,n=!1){const r=mv();let s=r;n&&(s+="/PASSWORD_RECOVERY"),await Sn(e,`${t}-code-verifier`,s);const i=await vv(r);return[i,r===i?"plain":"s256"]}const wv=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function xv(e){const t=e.headers.get(za);if(!t||!t.match(wv))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch{return null}}function _v(e){if(!e)throw new Error("Missing exp claim");const t=Math.floor(Date.now()/1e3);if(e<=t)throw new Error("JWT has expired")}function kv(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const Sv=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function vn(e){if(!Sv.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}function To(){const e={};return new Proxy(e,{get:(t,n)=>{if(n==="__isUserNotAvailableProxy")return!0;if(typeof n=="symbol"){const r=n.toString();if(r==="Symbol(Symbol.toPrimitive)"||r==="Symbol(Symbol.toStringTag)"||r==="Symbol(util.inspect.custom)")return}throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the "${n}" property of the session object is not supported. Please use getUser() instead.`)},set:(t,n)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the "${n}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)},deleteProperty:(t,n)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the "${n}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)}})}function Cc(e){return JSON.parse(JSON.stringify(e))}var bv=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n};const Jt=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Cv=[502,503,504];async function Pc(e){var t;if(!hv(e))throw new Ua(Jt(e),0);if(Cv.includes(e.status))throw new Ua(Jt(e),e.status);let n;try{n=await e.json()}catch(i){throw new Nf(Jt(i),i)}let r;const s=xv(e);if(s&&s.getTime()>=Mf["2024-01-01"].timestamp&&typeof n=="object"&&n&&typeof n.code=="string"?r=n.code:typeof n=="object"&&n&&typeof n.error_code=="string"&&(r=n.error_code),r){if(r==="weak_password")throw new _c(Jt(n),e.status,((t=n.weak_password)===null||t===void 0?void 0:t.reasons)||[]);if(r==="session_not_found")throw new vt}else if(typeof n=="object"&&n&&typeof n.weak_password=="object"&&n.weak_password&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.reasons.reduce((i,o)=>i&&typeof o=="string",!0))throw new _c(Jt(n),e.status,n.weak_password.reasons);throw new Yy(Jt(n),e.status||500,r)}const Pv=(e,t,n,r)=>{const s={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?s:(s.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t==null?void 0:t.headers),s.body=JSON.stringify(r),Object.assign(Object.assign({},s),n))};async function R(e,t,n,r){var s;const i=Object.assign({},r==null?void 0:r.headers);i[za]||(i[za]=Mf["2024-01-01"].name),r!=null&&r.jwt&&(i.Authorization=`Bearer ${r.jwt}`);const o=(s=r==null?void 0:r.query)!==null&&s!==void 0?s:{};r!=null&&r.redirectTo&&(o.redirect_to=r.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=await jv(e,t,n+a,{headers:i,noResolveJson:r==null?void 0:r.noResolveJson},{},r==null?void 0:r.body);return r!=null&&r.xform?r==null?void 0:r.xform(l):{data:Object.assign({},l),error:null}}async function jv(e,t,n,r,s,i){const o=Pv(t,r,s,i);let a;try{a=await e(n,Object.assign({},o))}catch(l){throw console.error(l),new Ua(Jt(l),0)}if(a.ok||await Pc(a),r!=null&&r.noResolveJson)return a;try{return await a.json()}catch(l){await Pc(l)}}function rt(e){var t;let n=null;Tv(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=uv(e.expires_in)));const r=(t=e.user)!==null&&t!==void 0?t:e;return{data:{session:n,user:r},error:null}}function jc(e){const t=rt(e);return!t.error&&e.weak_password&&typeof e.weak_password=="object"&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&typeof e.weak_password.message=="string"&&e.weak_password.reasons.reduce((n,r)=>n&&typeof r=="string",!0)&&(t.data.weak_password=e.weak_password),t}function St(e){var t;return{data:{user:(t=e.user)!==null&&t!==void 0?t:e},error:null}}function Ev(e){return{data:e,error:null}}function Mv(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:s,verification_type:i}=e,o=bv(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:t,email_otp:n,hashed_token:r,redirect_to:s,verification_type:i},l=Object.assign({},o);return{data:{properties:a,user:l},error:null}}function Nv(e){return e}function Tv(e){return e.access_token&&e.refresh_token&&e.expires_in}const Ao=["global","local","others"];var Av=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n};class $v{constructor({url:t="",headers:n={},fetch:r}){this.url=t,this.headers=n,this.fetch=$f(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,n=Ao[0]){if(Ao.indexOf(n)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${Ao.join(", ")}`);try{return await R(this.fetch,"POST",`${this.url}/logout?scope=${n}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(r){if(I(r))return{data:null,error:r};throw r}}async inviteUserByEmail(t,n={}){try{return await R(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:n.data},headers:this.headers,redirectTo:n.redirectTo,xform:St})}catch(r){if(I(r))return{data:{user:null},error:r};throw r}}async generateLink(t){try{const{options:n}=t,r=Av(t,["options"]),s=Object.assign(Object.assign({},r),n);return"newEmail"in r&&(s.new_email=r==null?void 0:r.newEmail,delete s.newEmail),await R(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:Mv,redirectTo:n==null?void 0:n.redirectTo})}catch(n){if(I(n))return{data:{properties:null,user:null},error:n};throw n}}async createUser(t){try{return await R(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:St})}catch(n){if(I(n))return{data:{user:null},error:n};throw n}}async listUsers(t){var n,r,s,i,o,a,l;try{const u={nextPage:null,lastPage:0,total:0},d=await R(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(n=t==null?void 0:t.page)===null||n===void 0?void 0:n.toString())!==null&&r!==void 0?r:"",per_page:(i=(s=t==null?void 0:t.perPage)===null||s===void 0?void 0:s.toString())!==null&&i!==void 0?i:""},xform:Nv});if(d.error)throw d.error;const h=await d.json(),f=(o=d.headers.get("x-total-count"))!==null&&o!==void 0?o:0,y=(l=(a=d.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&l!==void 0?l:[];return y.length>0&&(y.forEach(v=>{const x=parseInt(v.split(";")[0].split("=")[1].substring(0,1)),_=JSON.parse(v.split(";")[1].split("=")[1]);u[`${_}Page`]=x}),u.total=parseInt(f)),{data:Object.assign(Object.assign({},h),u),error:null}}catch(u){if(I(u))return{data:{users:[]},error:u};throw u}}async getUserById(t){vn(t);try{return await R(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:St})}catch(n){if(I(n))return{data:{user:null},error:n};throw n}}async updateUserById(t,n){vn(t);try{return await R(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:n,headers:this.headers,xform:St})}catch(r){if(I(r))return{data:{user:null},error:r};throw r}}async deleteUser(t,n=!1){vn(t);try{return await R(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:n},xform:St})}catch(r){if(I(r))return{data:{user:null},error:r};throw r}}async _listFactors(t){vn(t.userId);try{const{data:n,error:r}=await R(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:s=>({data:{factors:s},error:null})});return{data:n,error:r}}catch(n){if(I(n))return{data:null,error:n};throw n}}async _deleteFactor(t){vn(t.userId),vn(t.id);try{return{data:await R(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(n){if(I(n))return{data:null,error:n};throw n}}}function Ec(e={}){return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}function Ov(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const wn={debug:!!(globalThis&&Af()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class Of extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class Iv extends Of{}async function Rv(e,t,n){wn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),wn.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,t===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async s=>{if(s){wn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,s.name);try{return await n()}finally{wn.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,s.name)}}else{if(t===0)throw wn.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new Iv(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(wn.debug)try{const i=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(i,null,"  "))}catch(i){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",i)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}}))}Ov();const Lv={url:Ky,storageKey:Gy,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Jy,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function Mc(e,t,n){return await n()}const xn={};class Yr{constructor(t){var n,r;this.userStorage=null,this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=Yr.nextInstanceID,Yr.nextInstanceID+=1,this.instanceID>0&&ze()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const s=Object.assign(Object.assign({},Lv),t);if(this.logDebugMessages=!!s.debug,typeof s.debug=="function"&&(this.logger=s.debug),this.persistSession=s.persistSession,this.storageKey=s.storageKey,this.autoRefreshToken=s.autoRefreshToken,this.admin=new $v({url:s.url,headers:s.headers,fetch:s.fetch}),this.url=s.url,this.headers=s.headers,this.fetch=$f(s.fetch),this.lock=s.lock||Mc,this.detectSessionInUrl=s.detectSessionInUrl,this.flowType=s.flowType,this.hasCustomAuthorizationHeader=s.hasCustomAuthorizationHeader,s.lock?this.lock=s.lock:ze()&&(!((n=globalThis==null?void 0:globalThis.navigator)===null||n===void 0)&&n.locks)?this.lock=Rv:this.lock=Mc,this.jwks||(this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER),this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?(s.storage?this.storage=s.storage:Af()?this.storage=globalThis.localStorage:(this.memoryStorage={},this.storage=Ec(this.memoryStorage)),s.userStorage&&(this.userStorage=s.userStorage)):(this.memoryStorage={},this.storage=Ec(this.memoryStorage)),ze()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",i)}(r=this.broadcastChannel)===null||r===void 0||r.addEventListener("message",async i=>{this._debug("received broadcast notification from other tab or client",i),await this._notifyAllSubscribers(i.data.event,i.data.session,!1)})}this.initialize()}get jwks(){var t,n;return(n=(t=xn[this.storageKey])===null||t===void 0?void 0:t.jwks)!==null&&n!==void 0?n:{keys:[]}}set jwks(t){xn[this.storageKey]=Object.assign(Object.assign({},xn[this.storageKey]),{jwks:t})}get jwks_cached_at(){var t,n;return(n=(t=xn[this.storageKey])===null||t===void 0?void 0:t.cachedAt)!==null&&n!==void 0?n:Number.MIN_SAFE_INTEGER}set jwks_cached_at(t){xn[this.storageKey]=Object.assign(Object.assign({},xn[this.storageKey]),{cachedAt:t})}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Ef}) ${new Date().toISOString()}`,...t),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var t;try{const n=dv(window.location.href);let r="none";if(this._isImplicitGrantCallback(n)?r="implicit":await this._isPKCECallback(n)&&(r="pkce"),ze()&&this.detectSessionInUrl&&r!=="none"){const{data:s,error:i}=await this._getSessionFromURL(n,r);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),tv(i)){const l=(t=i.details)===null||t===void 0?void 0:t.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:i}}return await this._removeSession(),{error:i}}const{session:o,redirectType:a}=s;return this._debug("#_initialize()","detected session in URL",o,"redirect type",a),await this._saveSession(o),setTimeout(async()=>{a==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(n){return I(n)?{error:n}:{error:new Nf("Unexpected error during initialization",n)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var n,r,s;try{const i=await R(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(r=(n=t==null?void 0:t.options)===null||n===void 0?void 0:n.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:(s=t==null?void 0:t.options)===null||s===void 0?void 0:s.captchaToken}},xform:rt}),{data:o,error:a}=i;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(i){if(I(i))return{data:{user:null,session:null},error:i};throw i}}async signUp(t){var n,r,s;try{let i;if("email"in t){const{email:d,password:h,options:f}=t;let y=null,v=null;this.flowType==="pkce"&&([y,v]=await yn(this.storage,this.storageKey)),i=await R(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:f==null?void 0:f.emailRedirectTo,body:{email:d,password:h,data:(n=f==null?void 0:f.data)!==null&&n!==void 0?n:{},gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken},code_challenge:y,code_challenge_method:v},xform:rt})}else if("phone"in t){const{phone:d,password:h,options:f}=t;i=await R(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:d,password:h,data:(r=f==null?void 0:f.data)!==null&&r!==void 0?r:{},channel:(s=f==null?void 0:f.channel)!==null&&s!==void 0?s:"sms",gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken}},xform:rt})}else throw new Ts("You must provide either an email or phone number and a password");const{data:o,error:a}=i;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(i){if(I(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithPassword(t){try{let n;if("email"in t){const{email:i,password:o,options:a}=t;n=await R(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:i,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:jc})}else if("phone"in t){const{phone:i,password:o,options:a}=t;n=await R(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:i,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:jc})}else throw new Ts("You must provide either an email or phone number and a password");const{data:r,error:s}=n;return s?{data:{user:null,session:null},error:s}:!r||!r.session||!r.user?{data:{user:null,session:null},error:new Ns}:(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:s})}catch(n){if(I(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOAuth(t){var n,r,s,i;return await this._handleProviderSignIn(t.provider,{redirectTo:(n=t.options)===null||n===void 0?void 0:n.redirectTo,scopes:(r=t.options)===null||r===void 0?void 0:r.scopes,queryParams:(s=t.options)===null||s===void 0?void 0:s.queryParams,skipBrowserRedirect:(i=t.options)===null||i===void 0?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(t))}async signInWithWeb3(t){const{chain:n}=t;if(n==="solana")return await this.signInWithSolana(t);throw new Error(`@supabase/auth-js: Unsupported chain "${n}"`)}async signInWithSolana(t){var n,r,s,i,o,a,l,u,d,h,f,y;let v,x;if("message"in t)v=t.message,x=t.signature;else{const{chain:_,wallet:m,statement:p,options:g}=t;let w;if(ze())if(typeof m=="object")w=m;else{const b=window;if("solana"in b&&typeof b.solana=="object"&&("signIn"in b.solana&&typeof b.solana.signIn=="function"||"signMessage"in b.solana&&typeof b.solana.signMessage=="function"))w=b.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof m!="object"||!(g!=null&&g.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");w=m}const S=new URL((n=g==null?void 0:g.url)!==null&&n!==void 0?n:window.location.href);if("signIn"in w&&w.signIn){const b=await w.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},g==null?void 0:g.signInWithSolana),{version:"1",domain:S.host,uri:S.href}),p?{statement:p}:null));let P;if(Array.isArray(b)&&b[0]&&typeof b[0]=="object")P=b[0];else if(b&&typeof b=="object"&&"signedMessage"in b&&"signature"in b)P=b;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in P&&"signature"in P&&(typeof P.signedMessage=="string"||P.signedMessage instanceof Uint8Array)&&P.signature instanceof Uint8Array)v=typeof P.signedMessage=="string"?P.signedMessage:new TextDecoder().decode(P.signedMessage),x=P.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in w)||typeof w.signMessage!="function"||!("publicKey"in w)||typeof w!="object"||!w.publicKey||!("toBase58"in w.publicKey)||typeof w.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");v=[`${S.host} wants you to sign in with your Solana account:`,w.publicKey.toBase58(),...p?["",p,""]:[""],"Version: 1",`URI: ${S.href}`,`Issued At: ${(s=(r=g==null?void 0:g.signInWithSolana)===null||r===void 0?void 0:r.issuedAt)!==null&&s!==void 0?s:new Date().toISOString()}`,...!((i=g==null?void 0:g.signInWithSolana)===null||i===void 0)&&i.notBefore?[`Not Before: ${g.signInWithSolana.notBefore}`]:[],...!((o=g==null?void 0:g.signInWithSolana)===null||o===void 0)&&o.expirationTime?[`Expiration Time: ${g.signInWithSolana.expirationTime}`]:[],...!((a=g==null?void 0:g.signInWithSolana)===null||a===void 0)&&a.chainId?[`Chain ID: ${g.signInWithSolana.chainId}`]:[],...!((l=g==null?void 0:g.signInWithSolana)===null||l===void 0)&&l.nonce?[`Nonce: ${g.signInWithSolana.nonce}`]:[],...!((u=g==null?void 0:g.signInWithSolana)===null||u===void 0)&&u.requestId?[`Request ID: ${g.signInWithSolana.requestId}`]:[],...!((h=(d=g==null?void 0:g.signInWithSolana)===null||d===void 0?void 0:d.resources)===null||h===void 0)&&h.length?["Resources",...g.signInWithSolana.resources.map(P=>`- ${P}`)]:[]].join(`
`);const b=await w.signMessage(new TextEncoder().encode(v),"utf8");if(!b||!(b instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");x=b}}try{const{data:_,error:m}=await R(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:v,signature:lv(x)},!((f=t.options)===null||f===void 0)&&f.captchaToken?{gotrue_meta_security:{captcha_token:(y=t.options)===null||y===void 0?void 0:y.captchaToken}}:null),xform:rt});if(m)throw m;return!_||!_.session||!_.user?{data:{user:null,session:null},error:new Ns}:(_.session&&(await this._saveSession(_.session),await this._notifyAllSubscribers("SIGNED_IN",_.session)),{data:Object.assign({},_),error:m})}catch(_){if(I(_))return{data:{user:null,session:null},error:_};throw _}}async _exchangeCodeForSession(t){const n=await qt(this.storage,`${this.storageKey}-code-verifier`),[r,s]=(n??"").split("/");try{const{data:i,error:o}=await R(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:r},xform:rt});if(await yt(this.storage,`${this.storageKey}-code-verifier`),o)throw o;return!i||!i.session||!i.user?{data:{user:null,session:null,redirectType:null},error:new Ns}:(i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:Object.assign(Object.assign({},i),{redirectType:s??null}),error:o})}catch(i){if(I(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}}async signInWithIdToken(t){try{const{options:n,provider:r,token:s,access_token:i,nonce:o}=t,a=await R(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:s,access_token:i,nonce:o,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}},xform:rt}),{data:l,error:u}=a;return u?{data:{user:null,session:null},error:u}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new Ns}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:u})}catch(n){if(I(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOtp(t){var n,r,s,i,o;try{if("email"in t){const{email:a,options:l}=t;let u=null,d=null;this.flowType==="pkce"&&([u,d]=await yn(this.storage,this.storageKey));const{error:h}=await R(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:a,data:(n=l==null?void 0:l.data)!==null&&n!==void 0?n:{},create_user:(r=l==null?void 0:l.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:u,code_challenge_method:d},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:h}}if("phone"in t){const{phone:a,options:l}=t,{data:u,error:d}=await R(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:a,data:(s=l==null?void 0:l.data)!==null&&s!==void 0?s:{},create_user:(i=l==null?void 0:l.shouldCreateUser)!==null&&i!==void 0?i:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(o=l==null?void 0:l.channel)!==null&&o!==void 0?o:"sms"}});return{data:{user:null,session:null,messageId:u==null?void 0:u.message_id},error:d}}throw new Ts("You must provide either an email or phone number.")}catch(a){if(I(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(t){var n,r;try{let s,i;"options"in t&&(s=(n=t.options)===null||n===void 0?void 0:n.redirectTo,i=(r=t.options)===null||r===void 0?void 0:r.captchaToken);const{data:o,error:a}=await R(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:i}}),redirectTo:s,xform:rt});if(a)throw a;if(!o)throw new Error("An error occurred on token verification.");const l=o.session,u=o.user;return l!=null&&l.access_token&&(await this._saveSession(l),await this._notifyAllSubscribers(t.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(s){if(I(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithSSO(t){var n,r,s;try{let i=null,o=null;return this.flowType==="pkce"&&([i,o]=await yn(this.storage,this.storageKey)),await R(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:(r=(n=t.options)===null||n===void 0?void 0:n.redirectTo)!==null&&r!==void 0?r:void 0}),!((s=t==null?void 0:t.options)===null||s===void 0)&&s.captchaToken?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:o}),headers:this.headers,xform:Ev})}catch(i){if(I(i))return{data:null,error:i};throw i}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async t=>{const{data:{session:n},error:r}=t;if(r)throw r;if(!n)throw new vt;const{error:s}=await R(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:n.access_token});return{data:{user:null,session:null},error:s}})}catch(t){if(I(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const n=`${this.url}/resend`;if("email"in t){const{email:r,type:s,options:i}=t,{error:o}=await R(this.fetch,"POST",n,{headers:this.headers,body:{email:r,type:s,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}},redirectTo:i==null?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:o}}else if("phone"in t){const{phone:r,type:s,options:i}=t,{data:o,error:a}=await R(this.fetch,"POST",n,{headers:this.headers,body:{phone:r,type:s,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:o==null?void 0:o.message_id},error:a}}throw new Ts("You must provide either an email or phone number and a type")}catch(n){if(I(n))return{data:{user:null,session:null},error:n};throw n}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async n=>n))}async _acquireLock(t,n){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const r=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await r,await n()))();return this.pendingInLock.push((async()=>{try{await s}catch{}})()),s}return await this.lock(`lock:${this.storageKey}`,t,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const r=n();for(this.pendingInLock.push((async()=>{try{await r}catch{}})()),await r;this.pendingInLock.length;){const s=[...this.pendingInLock];await Promise.all(s),this.pendingInLock.splice(0,s.length)}return await r}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const n=await this.__loadSession();return await t(n)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let t=null;const n=await qt(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",n),n!==null&&(this._isValidSession(n)?t=n:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const r=t.expires_at?t.expires_at*1e3-Date.now()<Eo:!1;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",t.expires_at),!r){if(this.userStorage){const o=await qt(this.userStorage,this.storageKey+"-user");o!=null&&o.user?t.user=o.user:t.user=To()}if(this.storage.isServer&&t.user){let o=this.suppressGetSessionWarning;t=new Proxy(t,{get:(l,u,d)=>(!o&&u==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),o=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,u,d))})}return{data:{session:t},error:null}}const{session:s,error:i}=await this._callRefreshToken(t.refresh_token);return i?{data:{session:null},error:i}:{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){return t?await this._getUser(t):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(t){try{return t?await R(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:St}):await this._useSession(async n=>{var r,s,i;const{data:o,error:a}=n;if(a)throw a;return!(!((r=o.session)===null||r===void 0)&&r.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new vt}:await R(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(i=(s=o.session)===null||s===void 0?void 0:s.access_token)!==null&&i!==void 0?i:void 0,xform:St})})}catch(n){if(I(n))return ev(n)&&(await this._removeSession(),await yt(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:n};throw n}}async updateUser(t,n={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(t,n))}async _updateUser(t,n={}){try{return await this._useSession(async r=>{const{data:s,error:i}=r;if(i)throw i;if(!s.session)throw new vt;const o=s.session;let a=null,l=null;this.flowType==="pkce"&&t.email!=null&&([a,l]=await yn(this.storage,this.storageKey));const{data:u,error:d}=await R(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:n==null?void 0:n.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:a,code_challenge_method:l}),jwt:o.access_token,xform:St});if(d)throw d;return o.user=u.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(r){if(I(r))return{data:{user:null},error:r};throw r}}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(t))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new vt;const n=Date.now()/1e3;let r=n,s=!0,i=null;const{payload:o}=No(t.access_token);if(o.exp&&(r=o.exp,s=r<=n),s){const{session:a,error:l}=await this._callRefreshToken(t.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!a)return{data:{user:null,session:null},error:null};i=a}else{const{data:a,error:l}=await this._getUser(t.access_token);if(l)throw l;i={access_token:t.access_token,refresh_token:t.refresh_token,user:a.user,token_type:"bearer",expires_in:r-n,expires_at:r},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(n){if(I(n))return{data:{session:null,user:null},error:n};throw n}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(t))}async _refreshSession(t){try{return await this._useSession(async n=>{var r;if(!t){const{data:o,error:a}=n;if(a)throw a;t=(r=o.session)!==null&&r!==void 0?r:void 0}if(!(t!=null&&t.refresh_token))throw new vt;const{session:s,error:i}=await this._callRefreshToken(t.refresh_token);return i?{data:{user:null,session:null},error:i}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}})}catch(n){if(I(n))return{data:{user:null,session:null},error:n};throw n}}async _getSessionFromURL(t,n){try{if(!ze())throw new As("No browser detected.");if(t.error||t.error_description||t.error_code)throw new As(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(n){case"implicit":if(this.flowType==="pkce")throw new xc("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new As("Not a valid implicit grant flow url.");break;default:}if(n==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new xc("No code detected.");const{data:p,error:g}=await this._exchangeCodeForSession(t.code);if(g)throw g;const w=new URL(window.location.href);return w.searchParams.delete("code"),window.history.replaceState(window.history.state,"",w.toString()),{data:{session:p.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:s,access_token:i,refresh_token:o,expires_in:a,expires_at:l,token_type:u}=t;if(!i||!a||!o||!u)throw new As("No session defined in URL");const d=Math.round(Date.now()/1e3),h=parseInt(a);let f=d+h;l&&(f=parseInt(l));const y=f-d;y*1e3<=kn&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${y}s, should have been closer to ${h}s`);const v=f-h;d-v>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",v,f,d):d-v<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",v,f,d);const{data:x,error:_}=await this._getUser(i);if(_)throw _;const m={provider_token:r,provider_refresh_token:s,access_token:i,expires_in:h,expires_at:f,refresh_token:o,token_type:u,user:x.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:m,redirectType:t.type},error:null}}catch(r){if(I(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(t){return!!(t.access_token||t.error_description)}async _isPKCECallback(t){const n=await qt(this.storage,`${this.storageKey}-code-verifier`);return!!(t.code&&n)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(t))}async _signOut({scope:t}={scope:"global"}){return await this._useSession(async n=>{var r;const{data:s,error:i}=n;if(i)return{error:i};const o=(r=s.session)===null||r===void 0?void 0:r.access_token;if(o){const{error:a}=await this.admin.signOut(o,t);if(a&&!(Zy(a)&&(a.status===404||a.status===401||a.status===403)))return{error:a}}return t!=="others"&&(await this._removeSession(),await yt(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(t){const n=cv(),r={id:n,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",n),this.stateChangeEmitters.delete(n)}};return this._debug("#onAuthStateChange()","registered callback with id",n),this.stateChangeEmitters.set(n,r),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(n)})))(),{data:{subscription:r}}}async _emitInitialSession(t){return await this._useSession(async n=>{var r,s;try{const{data:{session:i},error:o}=n;if(o)throw o;await((r=this.stateChangeEmitters.get(t))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",t,"session",i)}catch(i){await((s=this.stateChangeEmitters.get(t))===null||s===void 0?void 0:s.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",i),console.error(i)}})}async resetPasswordForEmail(t,n={}){let r=null,s=null;this.flowType==="pkce"&&([r,s]=await yn(this.storage,this.storageKey,!0));try{return await R(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:r,code_challenge_method:s,gotrue_meta_security:{captcha_token:n.captchaToken}},headers:this.headers,redirectTo:n.redirectTo})}catch(i){if(I(i))return{data:null,error:i};throw i}}async getUserIdentities(){var t;try{const{data:n,error:r}=await this.getUser();if(r)throw r;return{data:{identities:(t=n.user.identities)!==null&&t!==void 0?t:[]},error:null}}catch(n){if(I(n))return{data:null,error:n};throw n}}async linkIdentity(t){var n;try{const{data:r,error:s}=await this._useSession(async i=>{var o,a,l,u,d;const{data:h,error:f}=i;if(f)throw f;const y=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:(o=t.options)===null||o===void 0?void 0:o.redirectTo,scopes:(a=t.options)===null||a===void 0?void 0:a.scopes,queryParams:(l=t.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return await R(this.fetch,"GET",y,{headers:this.headers,jwt:(d=(u=h.session)===null||u===void 0?void 0:u.access_token)!==null&&d!==void 0?d:void 0})});if(s)throw s;return ze()&&!(!((n=t.options)===null||n===void 0)&&n.skipBrowserRedirect)&&window.location.assign(r==null?void 0:r.url),{data:{provider:t.provider,url:r==null?void 0:r.url},error:null}}catch(r){if(I(r))return{data:{provider:t.provider,url:null},error:r};throw r}}async unlinkIdentity(t){try{return await this._useSession(async n=>{var r,s;const{data:i,error:o}=n;if(o)throw o;return await R(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:(s=(r=i.session)===null||r===void 0?void 0:r.access_token)!==null&&s!==void 0?s:void 0})})}catch(n){if(I(n))return{data:null,error:n};throw n}}async _refreshAccessToken(t){const n=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(n,"begin");try{const r=Date.now();return await pv(async s=>(s>0&&await fv(200*Math.pow(2,s-1)),this._debug(n,"refreshing attempt",s),await R(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:rt})),(s,i)=>{const o=200*Math.pow(2,s);return i&&Mo(i)&&Date.now()+o-r<kn})}catch(r){if(this._debug(n,"error",r),I(r))return{data:{session:null,user:null},error:r};throw r}finally{this._debug(n,"end")}}_isValidSession(t){return typeof t=="object"&&t!==null&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,n){const r=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:n.redirectTo,scopes:n.scopes,queryParams:n.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",n,"url",r),ze()&&!n.skipBrowserRedirect&&window.location.assign(r),{data:{provider:t,url:r},error:null}}async _recoverAndRefresh(){var t,n;const r="#_recoverAndRefresh()";this._debug(r,"begin");try{const s=await qt(this.storage,this.storageKey);if(s&&this.userStorage){let o=await qt(this.userStorage,this.storageKey+"-user");!this.storage.isServer&&Object.is(this.storage,this.userStorage)&&!o&&(o={user:s.user},await Sn(this.userStorage,this.storageKey+"-user",o)),s.user=(t=o==null?void 0:o.user)!==null&&t!==void 0?t:To()}else if(s&&!s.user&&!s.user){const o=await qt(this.storage,this.storageKey+"-user");o&&(o!=null&&o.user)?(s.user=o.user,await yt(this.storage,this.storageKey+"-user"),await Sn(this.storage,this.storageKey,s)):s.user=To()}if(this._debug(r,"session from storage",s),!this._isValidSession(s)){this._debug(r,"session is not valid"),s!==null&&await this._removeSession();return}const i=((n=s.expires_at)!==null&&n!==void 0?n:1/0)*1e3-Date.now()<Eo;if(this._debug(r,`session has${i?"":" not"} expired with margin of ${Eo}s`),i){if(this.autoRefreshToken&&s.refresh_token){const{error:o}=await this._callRefreshToken(s.refresh_token);o&&(console.error(o),Mo(o)||(this._debug(r,"refresh failed with a non-retryable error, removing the session",o),await this._removeSession()))}}else if(s.user&&s.user.__isUserNotAvailableProxy===!0)try{const{data:o,error:a}=await this._getUser(s.access_token);!a&&(o!=null&&o.user)?(s.user=o.user,await this._saveSession(s),await this._notifyAllSubscribers("SIGNED_IN",s)):this._debug(r,"could not get user data, skipping SIGNED_IN notification")}catch(o){console.error("Error getting user data:",o),this._debug(r,"error getting user data, skipping SIGNED_IN notification",o)}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(s){this._debug(r,"error",s),console.error(s);return}finally{this._debug(r,"end")}}async _callRefreshToken(t){var n,r;if(!t)throw new vt;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const s=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new Qi;const{data:i,error:o}=await this._refreshAccessToken(t);if(o)throw o;if(!i.session)throw new vt;await this._saveSession(i.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);const a={session:i.session,error:null};return this.refreshingDeferred.resolve(a),a}catch(i){if(this._debug(s,"error",i),I(i)){const o={session:null,error:i};return Mo(i)||await this._removeSession(),(n=this.refreshingDeferred)===null||n===void 0||n.resolve(o),o}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(i),i}finally{this.refreshingDeferred=null,this._debug(s,"end")}}async _notifyAllSubscribers(t,n,r=!0){const s=`#_notifyAllSubscribers(${t})`;this._debug(s,"begin",n,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:t,session:n});const i=[],o=Array.from(this.stateChangeEmitters.values()).map(async a=>{try{await a.callback(t,n)}catch(l){i.push(l)}});if(await Promise.all(o),i.length>0){for(let a=0;a<i.length;a+=1)console.error(i[a]);throw i[0]}}finally{this._debug(s,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0;const n=Object.assign({},t),r=n.user&&n.user.__isUserNotAvailableProxy===!0;if(this.userStorage){!r&&n.user&&await Sn(this.userStorage,this.storageKey+"-user",{user:n.user});const s=Object.assign({},n);delete s.user;const i=Cc(s);await Sn(this.storage,this.storageKey,i)}else{const s=Cc(n);await Sn(this.storage,this.storageKey,s)}}async _removeSession(){this._debug("#_removeSession()"),await yt(this.storage,this.storageKey),await yt(this.storage,this.storageKey+"-code-verifier"),await yt(this.storage,this.storageKey+"-user"),this.userStorage&&await yt(this.userStorage,this.storageKey+"-user"),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&ze()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",t)}catch(n){console.error("removing visibilitychange callback failed",n)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),kn);this.autoRefreshTicker=t,t&&typeof t=="object"&&typeof t.unref=="function"?t.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(t),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async n=>{const{data:{session:r}}=n;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const s=Math.floor((r.expires_at*1e3-t)/kn);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts ${kn}ms, refresh threshold is ${Fa} ticks`),s<=Fa&&await this._callRefreshToken(r.refresh_token)})}catch(n){console.error("Auto refresh tick failed with error. This is likely a transient error.",n)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(t){if(t.isAcquireTimeout||t instanceof Of)this._debug("auto refresh token tick lock not available");else throw t}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!ze()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){console.error("_handleVisibilityChange",t)}}async _onVisibilityChanged(t){const n=`#_onVisibilityChanged(${t})`;this._debug(n,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(n,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,n,r){const s=[`provider=${encodeURIComponent(n)}`];if(r!=null&&r.redirectTo&&s.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),r!=null&&r.scopes&&s.push(`scopes=${encodeURIComponent(r.scopes)}`),this.flowType==="pkce"){const[i,o]=await yn(this.storage,this.storageKey),a=new URLSearchParams({code_challenge:`${encodeURIComponent(i)}`,code_challenge_method:`${encodeURIComponent(o)}`});s.push(a.toString())}if(r!=null&&r.queryParams){const i=new URLSearchParams(r.queryParams);s.push(i.toString())}return r!=null&&r.skipBrowserRedirect&&s.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${t}?${s.join("&")}`}async _unenroll(t){try{return await this._useSession(async n=>{var r;const{data:s,error:i}=n;return i?{data:null,error:i}:await R(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(I(n))return{data:null,error:n};throw n}}async _enroll(t){try{return await this._useSession(async n=>{var r,s;const{data:i,error:o}=n;if(o)return{data:null,error:o};const a=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},t.factorType==="phone"?{phone:t.phone}:{issuer:t.issuer}),{data:l,error:u}=await R(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token});return u?{data:null,error:u}:(t.factorType==="totp"&&(!((s=l==null?void 0:l.totp)===null||s===void 0)&&s.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(n){if(I(n))return{data:null,error:n};throw n}}async _verify(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:s,error:i}=n;if(i)return{data:null,error:i};const{data:o,error:a}=await R(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:a})})}catch(n){if(I(n))return{data:null,error:n};throw n}})}async _challenge(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:s,error:i}=n;return i?{data:null,error:i}:await R(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(I(n))return{data:null,error:n};throw n}})}async _challengeAndVerify(t){const{data:n,error:r}=await this._challenge({factorId:t.factorId});return r?{data:null,error:r}:await this._verify({factorId:t.factorId,challengeId:n.id,code:t.code})}async _listFactors(){const{data:{user:t},error:n}=await this.getUser();if(n)return{data:null,error:n};const r=(t==null?void 0:t.factors)||[],s=r.filter(o=>o.factor_type==="totp"&&o.status==="verified"),i=r.filter(o=>o.factor_type==="phone"&&o.status==="verified");return{data:{all:r,totp:s,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async t=>{var n,r;const{data:{session:s},error:i}=t;if(i)return{data:null,error:i};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=No(s.access_token);let a=null;o.aal&&(a=o.aal);let l=a;((r=(n=s.user.factors)===null||n===void 0?void 0:n.filter(h=>h.status==="verified"))!==null&&r!==void 0?r:[]).length>0&&(l="aal2");const d=o.amr||[];return{data:{currentLevel:a,nextLevel:l,currentAuthenticationMethods:d},error:null}}))}async fetchJwk(t,n={keys:[]}){let r=n.keys.find(a=>a.kid===t);if(r)return r;const s=Date.now();if(r=this.jwks.keys.find(a=>a.kid===t),r&&this.jwks_cached_at+Xy>s)return r;const{data:i,error:o}=await R(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(o)throw o;return!i.keys||i.keys.length===0||(this.jwks=i,this.jwks_cached_at=s,r=i.keys.find(a=>a.kid===t),!r)?null:r}async getClaims(t,n={}){try{let r=t;if(!r){const{data:y,error:v}=await this.getSession();if(v||!y.session)return{data:null,error:v};r=y.session.access_token}const{header:s,payload:i,signature:o,raw:{header:a,payload:l}}=No(r);n!=null&&n.allowExpired||_v(i.exp);const u=!s.alg||s.alg.startsWith("HS")||!s.kid||!("crypto"in globalThis&&"subtle"in globalThis.crypto)?null:await this.fetchJwk(s.kid,n!=null&&n.keys?{keys:n.keys}:n==null?void 0:n.jwks);if(!u){const{error:y}=await this.getUser(r);if(y)throw y;return{data:{claims:i,header:s,signature:o},error:null}}const d=kv(s.alg),h=await crypto.subtle.importKey("jwk",u,d,!0,["verify"]);if(!await crypto.subtle.verify(d,h,o,av(`${a}.${l}`)))throw new Ba("Invalid JWT signature");return{data:{claims:i,header:s,signature:o},error:null}}catch(r){if(I(r))return{data:null,error:r};throw r}}}Yr.nextInstanceID=0;const Dv=Yr;class Fv extends Dv{constructor(t){super(t)}}var zv=function(e,t,n,r){function s(i){return i instanceof n?i:new n(function(o){o(i)})}return new(n||(n=Promise))(function(i,o){function a(d){try{u(r.next(d))}catch(h){o(h)}}function l(d){try{u(r.throw(d))}catch(h){o(h)}}function u(d){d.done?i(d.value):s(d.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class Uv{constructor(t,n,r){var s,i,o;if(this.supabaseUrl=t,this.supabaseKey=n,!t)throw new Error("supabaseUrl is required.");if(!n)throw new Error("supabaseKey is required.");const a=Vy(t),l=new URL(a);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const u=`sb-${l.hostname.split(".")[0]}-auth-token`,d={db:Ly,realtime:Fy,auth:Object.assign(Object.assign({},Dy),{storageKey:u}),global:Ry},h=qy(r??{},d);this.storageKey=(s=h.auth.storageKey)!==null&&s!==void 0?s:"",this.headers=(i=h.global.headers)!==null&&i!==void 0?i:{},h.accessToken?(this.accessToken=h.accessToken,this.auth=new Proxy({},{get:(f,y)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(y)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((o=h.auth)!==null&&o!==void 0?o:{},this.headers,h.global.fetch),this.fetch=Wy(n,this._getAccessToken.bind(this),h.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},h.realtime)),this.rest=new ry(new URL("rest/v1",l).href,{headers:this.headers,schema:h.db.schema,fetch:this.fetch}),this.storage=new $y(this.storageUrl.href,this.headers,this.fetch,r==null?void 0:r.storage),h.accessToken||this._listenForAuthEvents()}get functions(){return new Im(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,n={},r={}){return this.rest.rpc(t,n,r)}channel(t,n={config:{}}){return this.realtime.channel(t,n)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,n;return zv(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return(n=(t=r.session)===null||t===void 0?void 0:t.access_token)!==null&&n!==void 0?n:this.supabaseKey})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:s,storageKey:i,flowType:o,lock:a,debug:l},u,d){const h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Fv({url:this.authUrl.href,headers:Object.assign(Object.assign({},h),u),storageKey:i,autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:s,flowType:o,lock:a,debug:l,fetch:d,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new xy(this.realtimeUrl.href,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t==null?void 0:t.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((n,r)=>{this._handleTokenChanged(n,"CLIENT",r==null?void 0:r.access_token)})}_handleTokenChanged(t,n,r){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==r?this.changedAccessToken=r:t==="SIGNED_OUT"&&(this.realtime.setAuth(),n=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const Bv=(e,t,n)=>new Uv(e,t,n);function Wv(){if(typeof window<"u"||typeof process>"u")return!1;const e=process.version;if(e==null)return!1;const t=e.match(/^v(\d+)\./);return t?parseInt(t[1],10)<=18:!1}Wv()&&console.warn("⚠️  Node.js 18 and below are deprecated and will no longer be supported in future versions of @supabase/supabase-js. Please upgrade to Node.js 20 or later. For more information, visit: https://github.com/orgs/supabase/discussions/37217");const Hv="https://kecjfdtanszsllmnzhoy.supabase.co",Vv="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtlY2pmZHRhbnN6c2xsbW56aG95Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY3NDAzMTUsImV4cCI6MjA3MjMxNjMxNX0.98it84Jyax4ik1YuqnpMI5nbMffxVLe2EMSaeIPXxHE",qv=Hv,Kv=Vv,us=Bv(qv,Kv);function Gv(){return["sunday","monday","tuesday","wednesday","thursday","friday","saturday"][new Date().getDay()]}function Nc(e){if(!e||e.toLowerCase()==="closed")return null;const t=e.match(/(\d{1,2}):(\d{2})\s*(AM|PM)/i);if(!t)return null;let n=parseInt(t[1]);const r=parseInt(t[2]),s=t[3].toLowerCase();return s==="pm"&&n!==12&&(n+=12),s==="am"&&n===12&&(n=0),{hours:n,minutes:r}}function If(e){if(!e.hours)return{isOpen:!0,status:"Hours not available"};const t=new Date,n=Gv(),r=e.hours[n];if(!r||r.toLowerCase()==="closed"){const h=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"];let f=null;for(let y=1;y<=7;y++){const v=(t.getDay()+y)%7,x=h[v],_=e.hours[x];if(_&&_.toLowerCase()!=="closed"){f=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][v];break}}return{isOpen:!1,status:"Closed today",nextChange:f?`Opens ${f}`:void 0}}const s=r.match(/(\d{1,2}:\d{2}\s*(?:AM|PM))\s*-\s*(\d{1,2}:\d{2}\s*(?:AM|PM))/i);if(!s)return{isOpen:!0,status:"Open"};const i=Nc(s[1]),o=Nc(s[2]);if(!i||!o)return{isOpen:!0,status:"Open"};const a=t.getHours()*60+t.getMinutes(),l=i.hours*60+i.minutes,u=o.hours*60+o.minutes;if(a>=l&&a<u){const h=u-a,f=Math.floor(h/60),y=h%60;let v="";return h<=60?v=y>0?`Closes in ${f>0?f+"h ":""}${y}m`:"Closes in less than 1 minute":v=`Closes ${s[2]}`,{isOpen:!0,status:"Open",nextChange:v}}else{if(a<l)return{isOpen:!1,status:"Closed",nextChange:`Opens ${s[1]}`};{const h=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"],f=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],y=(t.getDay()+1)%7,v=h[y],x=e.hours[v];if(x&&x.toLowerCase()!=="closed"){const _=x.match(/(\d{1,2}:\d{2}\s*(?:AM|PM))/i);return{isOpen:!1,status:"Closed",nextChange:`Opens ${f[y]} ${_?_[1]:""}`}}for(let _=2;_<=7;_++){const m=(t.getDay()+_)%7,p=h[m],g=e.hours[p];if(g&&g.toLowerCase()!=="closed")return{isOpen:!1,status:"Closed",nextChange:`Opens ${f[m]}`}}return{isOpen:!1,status:"Closed"}}}}let xr=null;function Jv(e){xr=e}function Gl(){const[e,t]=k.useState([]);k.useEffect(()=>{const o=localStorage.getItem("dfw-bb-favorites");if(o)try{t(JSON.parse(o))}catch(a){console.error("Error parsing stored favorites:",a),t([])}},[]);const n=o=>{const a={businessName:o,addedAt:new Date().toISOString()},l=[...e,a];t(l),localStorage.setItem("dfw-bb-favorites",JSON.stringify(l)),xr&&xr("add",o)},r=o=>{const a=e.filter(l=>l.businessName!==o);t(a),localStorage.setItem("dfw-bb-favorites",JSON.stringify(a)),xr&&xr("remove",o)},s=o=>e.some(a=>a.businessName===o);return{favorites:e,addToFavorites:n,removeFromFavorites:r,isFavorite:s,toggleFavorite:o=>{s(o)?r(o):n(o)}}}/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qv=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Xv=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,n,r)=>r?r.toUpperCase():n.toLowerCase()),Tc=e=>{const t=Xv(e);return t.charAt(0).toUpperCase()+t.slice(1)},Rf=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim(),Yv=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Zv={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e1=k.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:s="",children:i,iconNode:o,...a},l)=>k.createElement("svg",{ref:l,...Zv,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Rf("lucide",s),...!i&&!Yv(a)&&{"aria-hidden":"true"},...a},[...o.map(([u,d])=>k.createElement(u,d)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=(e,t)=>{const n=k.forwardRef(({className:r,...s},i)=>k.createElement(e1,{ref:i,iconNode:t,className:Rf(`lucide-${Qv(Tc(e))}`,`lucide-${e}`,r),...s}));return n.displayName=Tc(e),n};/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const t1=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M17 17H4a1 1 0 0 1-.74-1.673C4.59 13.956 6 12.499 6 8a6 6 0 0 1 .258-1.742",key:"178tsu"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M8.668 3.01A6 6 0 0 1 18 8c0 2.687.77 4.653 1.707 6.05",key:"1hqiys"}]],n1=$("bell-off",t1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r1=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],Ac=$("bell",r1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s1=[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]],hr=$("briefcase",s1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i1=[["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M9 22v-3a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3",key:"cabbwy"}],["rect",{x:"4",y:"2",width:"16",height:"20",rx:"2",key:"1uxh74"}]],Lf=$("building",i1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const o1=[["path",{d:"M13.997 4a2 2 0 0 1 1.76 1.05l.486.9A2 2 0 0 0 18.003 7H20a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h1.997a2 2 0 0 0 1.759-1.048l.489-.904A2 2 0 0 1 10.004 4z",key:"18u6gg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]],a1=$("camera",o1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l1=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],u1=$("check",l1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c1=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]],d1=$("circle-plus",c1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h1=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],f1=$("clock",h1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p1=[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]],$c=$("coffee",p1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g1=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],m1=$("download",g1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y1=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]],Pi=$("external-link",y1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v1=[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]],w1=$("facebook",v1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x1=[["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}],["path",{d:"M12 16.5A4.5 4.5 0 1 1 7.5 12 4.5 4.5 0 1 1 12 7.5a4.5 4.5 0 1 1 4.5 4.5 4.5 4.5 0 1 1-4.5 4.5",key:"14wa3c"}],["path",{d:"M12 7.5V9",key:"1oy5b0"}],["path",{d:"M7.5 12H9",key:"eltsq1"}],["path",{d:"M16.5 12H15",key:"vk5kw4"}],["path",{d:"M12 16.5V15",key:"k7eayi"}],["path",{d:"m8 8 1.88 1.88",key:"nxy4qf"}],["path",{d:"M14.12 9.88 16 8",key:"1lst6k"}],["path",{d:"m8 16 1.88-1.88",key:"h2eex1"}],["path",{d:"M14.12 14.12 16 16",key:"uqkrx3"}]],Oc=$("flower",x1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _1=[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]],$o=$("gift",_1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k1=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],Df=$("globe",k1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S1=[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]],fr=$("graduation-cap",S1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b1=[["path",{d:"M2 9.5a5.5 5.5 0 0 1 9.591-3.676.56.56 0 0 0 .818 0A5.49 5.49 0 0 1 22 9.5c0 2.29-1.5 4-3 5.5l-5.492 5.313a2 2 0 0 1-3 .019L5 15c-1.5-1.5-3-3.2-3-5.5",key:"mvr1a0"}]],Ce=$("heart",b1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C1=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],Wa=$("house",C1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P1=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],j1=$("info",P1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E1=[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]],M1=$("instagram",E1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N1=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],Ff=$("map-pin",N1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T1=[["path",{d:"M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719",key:"1sd12s"}]],A1=$("message-circle",T1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $1=[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]],O1=$("monitor",$1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I1=[["path",{d:"M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401",key:"kfwtm"}]],R1=$("moon",I1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L1=[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]],$s=$("music",L1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D1=[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]],zf=$("navigation",D1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F1=[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]],Ic=$("palette",F1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z1=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],Uf=$("phone",z1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U1=[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]],Rc=$("plane",U1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B1=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],W1=$("plus",B1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H1=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],V1=$("refresh-cw",H1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q1=[["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["path",{d:"M8.12 8.12 12 12",key:"1alkpv"}],["path",{d:"M20 4 8.12 15.88",key:"xgtan2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M14.8 14.8 20 20",key:"ptml3r"}]],Os=$("scissors",q1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K1=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],G1=$("send",K1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const J1=[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Q1=$("settings",J1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X1=[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]],Bf=$("share-2",X1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y1=[["path",{d:"M12 2v13",key:"1km8f5"}],["path",{d:"m16 6-4-4-4 4",key:"13yo43"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}]],Z1=$("share",Y1);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ew=[["path",{d:"M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z",key:"1wgbhj"}]],Oo=$("shirt",ew);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tw=[["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}],["path",{d:"M3.103 6.034h17.794",key:"awc11p"}],["path",{d:"M3.4 5.467a2 2 0 0 0-.4 1.2V20a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6.667a2 2 0 0 0-.4-1.2l-2-2.667A2 2 0 0 0 17 2H7a2 2 0 0 0-1.6.8z",key:"o988cm"}]],Io=$("shopping-bag",tw);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nw=[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]],Is=$("smartphone",nw);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rw=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],Jl=$("star",rw);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sw=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],iw=$("sun",sw);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ow=[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]],aw=$("thumbs-up",ow);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lw=[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]],uw=$("trash-2",lw);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cw=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],ji=$("triangle-alert",cw);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dw=[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]],Lc=$("truck",dw);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hw=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],Rs=$("users",hw);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fw=[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]],Ls=$("utensils",fw);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pw=[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.106-3.105c.32-.322.863-.22.983.218a6 6 0 0 1-8.259 7.057l-7.91 7.91a1 1 0 0 1-2.999-3l7.91-7.91a6 6 0 0 1 7.057-8.259c.438.12.54.662.219.984z",key:"1ngwbx"}]],_n=$("wrench",pw);/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gw=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],cs=$("x",gw);function Wf({isFavorited:e,onToggle:t,size:n="md"}){const r={sm:"w-6 h-6",md:"w-8 h-8",lg:"w-10 h-10"},s={sm:16,md:20,lg:24};return c.jsx("button",{onClick:i=>{i.preventDefault(),i.stopPropagation(),t()},className:`
        ${r[n]} 
        flex items-center justify-center 
        rounded-full 
        transition-all duration-200 
        ${e?"bg-red-100 text-red-600 hover:bg-red-200":"bg-gray-100 text-gray-400 hover:bg-gray-200 hover:text-red-500"}
      `,"aria-label":e?"Remove from favorites":"Add to favorites",title:e?"Remove from favorites":"Add to favorites",children:c.jsx(Ce,{size:s[n],fill:e?"currentColor":"none",className:"transition-colors duration-200"})})}const mw={Restaurant:Ls,"Food & Dining":Ls,Bakery:Ls,Catering:Ls,"Coffee Shop":$c,Bar:$c,"Food Truck":Lc,Retail:Io,Shopping:Io,Grocery:Io,Clothing:Oo,Fashion:Oo,Boutique:Oo,Bookstore:fr,Beauty:Os,"Hair Salon":Os,"Barber Shop":Os,Spa:Ce,"Nail Salon":Os,Skincare:Ce,Healthcare:Ce,Medical:Ce,Dental:Ce,Fitness:Ce,Gym:Ce,Wellness:Ce,Therapy:Ce,Legal:hr,Accounting:hr,Consulting:hr,"Real Estate":Wa,Insurance:hr,"Financial Services":hr,"Auto Repair":_n,"Auto Services":_n,"Home Repair":_n,Plumbing:_n,Electrical:_n,HVAC:_n,Cleaning:Wa,Landscaping:Oc,"Arts & Crafts":Ic,Entertainment:$s,Photography:a1,Music:$s,Dance:$s,Theater:$s,Gallery:Ic,Education:fr,Training:fr,Tutoring:fr,School:fr,Technology:Is,"IT Services":Is,"Web Design":Is,Electronics:Is,Travel:Rc,Transportation:Lc,Tourism:Rc,Hotel:Lf,"Event Planning":$o,"Wedding Services":$o,Florist:Oc,"Party Supplies":$o,Community:Rs,"Non-Profit":Rs,Religious:Rs,"Social Services":Rs},yw=Lf;function Ha(e){return mw[e]||yw}const Hf="dfwbb:business-changes",vw="dfwbb:last-business-check",Vf="dfwbb:business-snapshots";function ww(){try{return JSON.parse(localStorage.getItem(Hf)||"[]")}catch{return[]}}function qf(e){const t=ww(),n={...e,id:crypto.randomUUID(),detectedAt:new Date().toISOString()};t.unshift(n);const r=t.slice(0,100);localStorage.setItem(Hf,JSON.stringify(r))}function Dc(e){const t={timestamp:new Date().toISOString(),businesses:e.map(n=>({id:n.id,name:n.name,phone:n.phone,address:n.address,website:n.website,coordinates:n.coordinates,status:n.status,hours:n.hours}))};localStorage.setItem(Vf,JSON.stringify(t)),localStorage.setItem(vw,new Date().toISOString())}function xw(){try{return JSON.parse(localStorage.getItem(Vf)||"null")}catch{return null}}function _w(e){const t=xw();if(!t)return Dc(e),[];const n=[];return e.forEach(r=>{if(!r.id)return;const s=t.businesses.find(a=>a.id===r.id);if(!s)return;s.phone!==r.phone&&n.push({businessId:r.id,businessName:r.name,changeType:"phone_change",oldValue:s.phone||"Not provided",newValue:r.phone||"Not provided",verified:!1,source:"automated_check"}),s.address!==r.address&&n.push({businessId:r.id,businessName:r.name,changeType:"address_change",oldValue:s.address||"Not provided",newValue:r.address||"Not provided",verified:!1,source:"automated_check"}),s.website!==r.website&&n.push({businessId:r.id,businessName:r.name,changeType:"website_change",oldValue:s.website||"Not provided",newValue:r.website||"Not provided",verified:!1,source:"automated_check"});const i=s.coordinates,o=r.coordinates;i&&o&&(i.latitude!==o.latitude||i.longitude!==o.longitude)&&n.push({businessId:r.id,businessName:r.name,changeType:"location_change",oldValue:`${i.latitude}, ${i.longitude}`,newValue:`${o.latitude}, ${o.longitude}`,verified:!1,source:"automated_check"}),s.status!==r.status&&n.push({businessId:r.id,businessName:r.name,changeType:"status_change",oldValue:s.status||"unknown",newValue:r.status||"unknown",verified:!1,source:"automated_check"})}),n.forEach(r=>qf(r)),Dc(e),n.map(r=>({...r,id:crypto.randomUUID(),detectedAt:new Date().toISOString()}))}function kw(e,t,n,r,s){qf({businessId:e,businessName:t,changeType:n,oldValue:"Current information",newValue:r,verified:!1,source:"user_report",reportedBy:s})}function Sw({isOpen:e,onClose:t,businessId:n,businessName:r}){const[s,i]=k.useState("phone_change"),[o,a]=k.useState(""),[l,u]=k.useState(""),[d,h]=k.useState(!1),f=[{value:"phone_change",label:"Phone Number Issue"},{value:"address_change",label:"Address Issue"},{value:"location_change",label:"Location Issue"},{value:"website_change",label:"Website Issue"},{value:"hours_change",label:"Hours Issue"},{value:"status_change",label:"Business Status Issue"}],y=async v=>{if(v.preventDefault(),!!o.trim()){h(!0);try{kw(n,r,s,o.trim(),l.trim()||void 0),a(""),u(""),i("phone_change"),alert("Thank you for reporting this issue! We will review it shortly."),t()}catch{alert("There was an error submitting your report. Please try again.")}finally{h(!1)}}};return e?c.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50",children:c.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto",children:[c.jsxs("div",{className:"flex items-center justify-between p-4 border-b dark:border-gray-700",children:[c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx(ji,{className:"text-yellow-600 dark:text-yellow-400",size:20}),c.jsx("h2",{className:"text-lg font-semibold",children:"Report an Issue"})]}),c.jsx("button",{onClick:t,className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:c.jsx(cs,{size:20})})]}),c.jsxs("div",{className:"p-4",children:[c.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:["Help us keep business information accurate by reporting any issues you find with ",c.jsx("strong",{children:r}),"."]}),c.jsxs("form",{onSubmit:y,className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Issue Type"}),c.jsx("select",{value:s,onChange:v=>i(v.target.value),className:"w-full px-3 py-2 border dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:f.map(v=>c.jsx("option",{value:v.value,children:v.label},v.value))})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Description *"}),c.jsx("textarea",{value:o,onChange:v=>a(v.target.value),placeholder:"Please describe the issue in detail...",rows:4,required:!0,className:"w-full px-3 py-2 border dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-sm font-medium mb-2",children:"Your Name (Optional)"}),c.jsx("input",{type:"text",value:l,onChange:v=>u(v.target.value),placeholder:"Enter your name (optional)",className:"w-full px-3 py-2 border dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),c.jsxs("div",{className:"flex gap-2 pt-4",children:[c.jsx("button",{type:"button",onClick:t,className:"flex-1 px-4 py-2 border dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Cancel"}),c.jsx("button",{type:"submit",disabled:!o.trim()||d,className:"flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors",children:d?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Submitting..."]}):c.jsxs(c.Fragment,{children:[c.jsx(G1,{size:16}),"Submit Report"]})})]})]})]})]})}):null}function Va({b:e,onClick:t}){const{isFavorite:n,toggleFavorite:r}=Gl(),[s,i]=k.useState(!1),o=y=>{r(e.name)},a=async y=>{y.stopPropagation();const v=`Check out ${e.name} in ${e.city}!`,x=e.website||window.location.href;try{navigator.share?await navigator.share({title:e.name,text:v,url:x}):(await navigator.clipboard.writeText(x),alert("Link copied to clipboard!"))}catch{}},l=y=>{y.stopPropagation(),i(!0)},u=y=>{y.stopPropagation()},d=y=>Array.from({length:5},(v,x)=>c.jsx(Jl,{size:16,className:`${x<y?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"}`},x)),h=If(e),f=Ha(e.category);return c.jsxs(c.Fragment,{children:[c.jsxs("div",{className:"rounded-xl border border-gray-200 dark:border-gray-700 p-4 shadow-sm bg-white dark:bg-gray-800 cursor-pointer hover:shadow-md dark:hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1",onClick:t,children:[c.jsxs("div",{className:"flex justify-between items-start gap-2",children:[c.jsxs("div",{className:"flex-1",children:[c.jsxs("div",{className:"flex items-start justify-between",children:[c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center",children:c.jsx(f,{size:16,className:"text-blue-600 dark:text-blue-400"})}),c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e.name}),c.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400",children:[c.jsx("span",{children:e.category}),c.jsx("span",{children:"•"}),c.jsx("span",{children:e.city}),e.distance&&c.jsxs(c.Fragment,{children:[c.jsx("span",{children:"•"}),c.jsxs("div",{className:"flex items-center gap-1 text-blue-600 dark:text-blue-400",children:[c.jsx(zf,{size:12}),c.jsxs("span",{children:[e.distance.toFixed(1)," mi"]})]})]})]})]})]}),c.jsx("div",{className:`px-2 py-1 rounded-full text-xs font-medium ${h.isOpen?"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300":"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"}`,children:h.status})]}),h.nextChange&&c.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-300 mt-1 ml-10",children:h.nextChange}),e.rating&&c.jsxs("div",{className:"flex items-center gap-1 mt-1 ml-10",children:[c.jsx("div",{className:"flex text-sm",children:d(Math.floor(e.rating))}),c.jsxs("span",{className:"text-xs text-gray-600 dark:text-gray-300",children:[e.rating.toFixed(1)," (",e.reviewCount||0,")"]})]}),e.price&&c.jsx("div",{className:"mt-1 ml-10",children:c.jsx("span",{className:"text-green-600 dark:text-green-400 font-medium text-sm",children:e.price})}),e.features&&e.features.length>0&&c.jsx("div",{className:"mt-2 ml-10",children:c.jsxs("div",{className:"flex flex-wrap gap-1",children:[e.features.slice(0,2).map((y,v)=>c.jsx("span",{className:"px-2 py-1 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs rounded-full",children:y},v)),e.features.length>2&&c.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400 self-center",children:["+",e.features.length-2," more"]})]})})]}),c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx(Wf,{isFavorited:n(e.name),onToggle:()=>o(),size:"sm"}),c.jsx("button",{onClick:a,className:"w-6 h-6 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors","aria-label":"Share business",title:"Share business",children:c.jsx(Bf,{size:14,className:"text-gray-600 dark:text-gray-300"})}),c.jsx("button",{onClick:l,className:"w-6 h-6 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors","aria-label":"Report an issue",title:"Report incorrect information",children:c.jsx(ji,{size:14,className:"text-yellow-600 dark:text-yellow-400"})})]})]}),e.description&&c.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-300 line-clamp-2 ml-10",children:e.description}),c.jsxs("div",{className:"mt-3 flex gap-3 items-center ml-10",children:[e.website&&c.jsxs("a",{href:e.website,target:"_blank",className:"text-blue-600 dark:text-blue-400 hover:underline text-sm inline-flex items-center gap-1",rel:"noreferrer noopener",onClick:u,children:[c.jsx(Df,{size:14}),"Website"]}),e.phone&&c.jsxs("a",{href:`tel:${e.phone}`,className:"text-blue-600 dark:text-blue-400 hover:underline text-sm inline-flex items-center gap-1",onClick:y=>y.stopPropagation(),children:[c.jsx(Uf,{size:14}),"Call"]}),c.jsx("span",{className:"text-blue-600 dark:text-blue-400 text-sm ml-auto font-medium",children:"View Details →"})]})]}),e.id&&c.jsx(Sw,{isOpen:s,onClose:()=>i(!1),businessId:e.id,businessName:e.name})]})}function bw({businessName:e,website:t,phone:n,address:r,averageRating:s=0,totalReviews:i=0}){const o=(d,h="sm")=>{const f=h==="sm"?16:20;return c.jsx("div",{className:"flex items-center gap-0.5",children:[1,2,3,4,5].map(y=>c.jsx(Jl,{size:f,className:`${y<=d?"text-yellow-400 fill-current":"text-gray-300"}`},y))})},a=()=>{const d=`${e} reviews`;return r?`https://www.google.com/search?q=${encodeURIComponent(`${e} ${r} reviews`)}`:`https://www.google.com/search?q=${encodeURIComponent(d)}`},l=()=>`https://www.yelp.com/search?find_desc=${encodeURIComponent(e)}`,u=[{name:"Google Reviews",icon:c.jsx(Pi,{size:20}),url:a(),description:"View and write reviews on Google",color:"bg-blue-500 hover:bg-blue-600"},{name:"Yelp",icon:c.jsx(A1,{size:20}),url:l(),description:"Check out reviews and photos on Yelp",color:"bg-red-500 hover:bg-red-600"}];return c.jsxs("div",{className:"space-y-6",children:[s>0&&c.jsx("div",{className:"bg-gray-50 rounded-lg p-6 text-center",children:c.jsxs("div",{className:"flex flex-col items-center gap-3",children:[c.jsx("div",{className:"text-4xl font-bold text-gray-900",children:s.toFixed(1)}),o(s,"md"),c.jsxs("p",{className:"text-gray-600",children:["Based on ",i," review",i!==1?"s":""," across platforms"]})]})}),c.jsxs("div",{children:[c.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[c.jsx(aw,{size:20}),"Find Reviews & Leave Feedback"]}),c.jsx("div",{className:"space-y-3",children:u.map(d=>c.jsx("a",{href:d.url,target:"_blank",rel:"noopener noreferrer",className:`block p-4 rounded-lg border-2 border-transparent hover:border-gray-200 transition-all duration-200 group ${d.color} text-white hover:shadow-md`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center gap-3",children:[d.icon,c.jsxs("div",{children:[c.jsx("h4",{className:"font-semibold",children:d.name}),c.jsx("p",{className:"text-sm opacity-90",children:d.description})]})]}),c.jsx(Pi,{size:16,className:"opacity-75 group-hover:opacity-100"})]})},d.name))})]}),c.jsxs("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-200",children:[c.jsx("h4",{className:"font-semibold text-blue-900 mb-2",children:"Help Other Customers"}),c.jsxs("p",{className:"text-blue-800 text-sm",children:["Share your experience with ",e," by leaving a review on Google or Yelp. Your feedback helps other customers make informed decisions and supports local businesses."]})]})]})}function Kf({business:e,isOpen:t,onClose:n}){var y;const[r,s]=k.useState("info"),{isFavorite:i,toggleFavorite:o}=Gl();if(!t||!e)return null;const a=()=>{o(e.name)},l=async()=>{const v=`Check out ${e.name} in ${e.city}!`,x=e.website||window.location.href;try{navigator.share?await navigator.share({title:e.name,text:v,url:x}):(await navigator.clipboard.writeText(x),alert("Link copied to clipboard!"))}catch{}},u=v=>Array.from({length:5},(x,_)=>c.jsx(Jl,{size:16,className:`${_<v?"text-yellow-400 fill-current":"text-gray-300"}`},_)),d=v=>{const _=v.replace(/\D/g,"").match(/^(\d{3})(\d{3})(\d{4})$/);return _?`(${_[1]}) ${_[2]}-${_[3]}`:v},h=()=>{if(!e.hours)return null;const v=["monday","tuesday","wednesday","thursday","friday","saturday","sunday"],x=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"];return v.map((_,m)=>{var p;return{day:x[m],hours:((p=e.hours)==null?void 0:p[_])||"Closed"}})},f=e?If(e):null;return c.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:n,children:c.jsxs("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col",onClick:v=>v.stopPropagation(),children:[c.jsx("div",{className:"p-6 border-b bg-white",children:c.jsxs("div",{className:"flex justify-between items-start",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:e.name}),c.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:[e.category," • ",e.city]}),f&&c.jsxs("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mt-2 ${f.isOpen?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:[c.jsx("div",{className:`w-2 h-2 rounded-full mr-2 ${f.isOpen?"bg-green-400":"bg-red-400"}`}),f.status,f.nextChange&&c.jsxs("span",{className:"ml-1 text-xs opacity-75",children:["• ",f.nextChange]})]}),e.rating&&c.jsxs("div",{className:"flex items-center mt-2",children:[c.jsx("div",{className:"flex mr-2",children:u(Math.floor(e.rating))}),c.jsxs("span",{className:"text-sm text-gray-600",children:[e.rating.toFixed(1)," (",e.reviewCount||0," reviews)"]})]}),e.price&&c.jsx("div",{className:"mt-2",children:c.jsx("span",{className:"text-green-600 font-medium",children:e.price})})]}),c.jsxs("div",{className:"flex gap-2 ml-4",children:[c.jsx(Wf,{isFavorited:i(e.name),onToggle:a}),c.jsx("button",{onClick:l,className:"w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 transition-colors","aria-label":"Share business",children:c.jsx(Bf,{size:16})}),c.jsx("button",{onClick:n,className:"w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 transition-colors","aria-label":"Close modal",children:c.jsx(cs,{size:16})})]})]})}),c.jsxs("div",{className:"flex border-b bg-gray-50",children:[c.jsx("button",{className:`flex-1 px-4 py-3 text-sm font-medium ${r==="info"?"border-b-2 border-blue-500 bg-white text-blue-600":"text-gray-500 hover:text-gray-700"}`,onClick:()=>s("info"),children:"Business Info"}),c.jsxs("button",{className:`flex-1 px-4 py-3 text-sm font-medium ${r==="reviews"?"border-b-2 border-blue-500 bg-white text-blue-600":"text-gray-500 hover:text-gray-700"}`,onClick:()=>s("reviews"),children:["Reviews (",e.reviewCount||0,")"]})]}),c.jsxs("div",{className:"flex-1 overflow-y-auto p-6",children:[r==="info"&&c.jsxs("div",{className:"space-y-6",children:[e.description&&c.jsxs("div",{children:[c.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"About"}),c.jsx("p",{className:"text-gray-700",children:e.description})]}),e.features&&e.features.length>0&&c.jsxs("div",{children:[c.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"Features"}),c.jsx("div",{className:"flex flex-wrap gap-2",children:e.features.map((v,x)=>c.jsx("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full",children:v},x))})]}),c.jsxs("div",{children:[c.jsx("h3",{className:"font-semibold text-gray-900 mb-3",children:"Contact Information"}),c.jsxs("div",{className:"space-y-3",children:[e.address&&c.jsxs("div",{className:"flex items-start gap-3",children:[c.jsx(Ff,{size:20,className:"text-gray-500 mt-0.5"}),c.jsxs("div",{className:"flex-1",children:[c.jsx("span",{className:"text-gray-700",children:e.address}),c.jsx("br",{}),c.jsxs("button",{onClick:()=>{const v=encodeURIComponent(`${e.name} ${e.address}`);window.open(`https://maps.google.com?q=${v}`,"_blank")},className:"text-blue-600 hover:underline text-sm mt-1 inline-flex items-center gap-1",children:["View on Google Maps ",c.jsx(Pi,{size:14})]})]})]}),e.phone&&c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx(Uf,{size:20,className:"text-gray-500"}),c.jsx("a",{href:`tel:${e.phone}`,className:"text-blue-600 hover:underline",children:d(e.phone)})]}),e.website&&c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx(Df,{size:20,className:"text-gray-500"}),c.jsxs("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline inline-flex items-center gap-1",children:["Visit Website ",c.jsx(Pi,{size:14})]})]})]})]}),e.hours&&c.jsxs("div",{children:[c.jsxs("h3",{className:"font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[c.jsx(f1,{size:20}),"Hours"]}),c.jsx("div",{className:"space-y-1 bg-gray-50 rounded-lg p-4",children:(y=h())==null?void 0:y.map(({day:v,hours:x})=>c.jsxs("div",{className:"flex justify-between",children:[c.jsxs("span",{className:"text-gray-600 font-medium",children:[v,":"]}),c.jsx("span",{className:"text-gray-700",children:x})]},v))})]}),e.social&&Object.keys(e.social).length>0&&c.jsxs("div",{children:[c.jsx("h3",{className:"font-semibold text-gray-900 mb-3",children:"Social Media"}),c.jsx("div",{className:"flex gap-3",children:Object.entries(e.social).map(([v,x])=>{const _=v==="instagram"?M1:w1;return c.jsxs("a",{href:x,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm capitalize transition-colors",children:[c.jsx(_,{size:16}),v]},v)})})]})]}),r==="reviews"&&c.jsx(bw,{businessName:e.name,website:e.website,phone:e.phone,address:e.address,averageRating:e.rating,totalReviews:e.reviewCount})]}),c.jsxs("div",{className:"p-4 border-t bg-gray-50 flex gap-3",children:[e.phone&&c.jsx("button",{onClick:()=>window.open(`tel:${e.phone}`),className:"flex-1 bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium",children:"Call Now"}),e.website&&c.jsx("button",{onClick:()=>window.open(e.website,"_blank"),className:"flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium",children:"Visit Website"})]})]})})}function Cw({value:e,onChange:t,businesses:n,placeholder:r="Search businesses, categories, or cities...",onBusinessSelect:s,className:i=""}){const[o,a]=k.useState(!1),[l,u]=k.useState(-1),d=k.useRef(null),h=k.useRef(null),f=Mi.useMemo(()=>{if(!e||e.length<2)return[];const w=e.toLowerCase(),S=[],b=new Set;n.forEach(O=>{if(O.name.toLowerCase().includes(w)){const A=`business-${O.name}`;b.has(A)||(b.add(A),S.push({type:"business",value:O.name,label:O.name,business:O}))}});const P=new Map;n.forEach(O=>{O.category.toLowerCase().includes(w)&&P.set(O.category,(P.get(O.category)||0)+1)}),P.forEach((O,A)=>{const q=`category-${A}`;b.has(q)||(b.add(q),S.push({type:"category",value:A,label:A,count:O}))});const M=new Map;return n.forEach(O=>{O.city.toLowerCase().includes(w)&&M.set(O.city,(M.get(O.city)||0)+1)}),M.forEach((O,A)=>{const q=`city-${A}`;b.has(q)||(b.add(q),S.push({type:"city",value:A,label:A,count:O}))}),S.sort((O,A)=>{const q=O.label.toLowerCase()===w,Ge=A.label.toLowerCase()===w;if(q&&!Ge)return-1;if(!q&&Ge)return 1;const Je=O.label.toLowerCase().startsWith(w),gt=A.label.toLowerCase().startsWith(w);return Je&&!gt?-1:!Je&&gt?1:O.label.localeCompare(A.label)}).slice(0,8)},[e,n]),y=w=>{const S=w.target.value;t(S),a(S.length>=2),u(-1)},v=()=>{e.length>=2&&a(!0)},x=()=>{setTimeout(()=>a(!1),200)},_=w=>{var S;w.type==="business"&&w.business&&s?s(w.business):t(w.label),a(!1),(S=d.current)==null||S.blur()},m=w=>{var S;if(o)switch(w.key){case"ArrowDown":w.preventDefault(),u(b=>b<f.length-1?b+1:0);break;case"ArrowUp":w.preventDefault(),u(b=>b>0?b-1:f.length-1);break;case"Enter":w.preventDefault(),l>=0&&_(f[l]);break;case"Escape":a(!1),u(-1),(S=d.current)==null||S.blur();break}},p=w=>{switch(w.type){case"business":if(w.business){const b=Ha(w.business.category);return c.jsx(b,{size:16,className:"text-blue-600"})}return c.jsx("span",{className:"text-blue-600",children:"🏢"});case"category":const S=Ha(w.value);return c.jsx(S,{size:16,className:"text-green-600"});case"city":return c.jsx("span",{className:"text-purple-600",children:"📍"});default:return null}},g=w=>{switch(w){case"business":return"Business";case"category":return"Category";case"city":return"City"}};return c.jsxs("div",{className:`relative ${i}`,children:[c.jsx("input",{ref:d,type:"text",value:e,onChange:y,onFocus:v,onBlur:x,onKeyDown:m,placeholder:r,className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white",autoComplete:"off"}),o&&f.length>0&&c.jsx("div",{ref:h,className:"absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto",children:f.map((w,S)=>c.jsxs("div",{className:`flex items-center gap-3 px-4 py-3 cursor-pointer transition-colors ${S===l?"bg-blue-50 dark:bg-blue-900/20":"hover:bg-gray-50 dark:hover:bg-gray-700"}`,onClick:()=>_(w),children:[c.jsx("div",{className:"flex-shrink-0",children:p(w)}),c.jsxs("div",{className:"flex-1 min-w-0",children:[c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx("span",{className:"font-medium text-gray-900 dark:text-white truncate",children:w.label}),w.count&&c.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["(",w.count,")"]})]}),c.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[g(w.type),w.business&&c.jsxs(c.Fragment,{children:[" • ",c.jsxs("span",{children:[w.business.category," • ",w.business.city]})]})]})]})]},`${w.type}-${w.value}`))})]})}function Pw(){return c.jsxs("div",{className:"rounded-xl border p-4 shadow-sm bg-white animate-pulse",children:[c.jsxs("div",{className:"flex justify-between items-start gap-2 mb-3",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:"h-5 bg-gray-200 rounded-md w-3/4 mb-2"}),c.jsx("div",{className:"h-4 bg-gray-200 rounded-md w-1/2 mb-2"}),c.jsx("div",{className:"h-3 bg-gray-200 rounded-md w-1/3"})]}),c.jsx("div",{className:"h-8 w-8 bg-gray-200 rounded"})]}),c.jsxs("div",{className:"space-y-2 mb-3",children:[c.jsx("div",{className:"h-3 bg-gray-200 rounded w-full"}),c.jsx("div",{className:"h-3 bg-gray-200 rounded w-4/5"})]}),c.jsxs("div",{className:"flex gap-2",children:[c.jsx("div",{className:"h-4 bg-gray-200 rounded w-16"}),c.jsx("div",{className:"h-4 bg-gray-200 rounded w-12"}),c.jsx("div",{className:"h-4 bg-gray-200 rounded w-20 ml-auto"})]})]})}function jw({count:e=6}){return c.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:Array.from({length:e}).map((t,n)=>c.jsx(Pw,{},n))})}function Ew(e,t=250){const[n,r]=k.useState(e);return k.useEffect(()=>{const s=setTimeout(()=>r(e),t);return()=>clearTimeout(s)},[e,t]),n}function Mw({onRefresh:e,threshold:t=80}){const[n,r]=k.useState(!1),[s,i]=k.useState(!1),[o,a]=k.useState(0);let l=0,u=0,d=!1;const h=k.useCallback(x=>{window.scrollY>5||(l=x.touches[0].clientY,d=!1)},[]),f=k.useCallback(x=>{if(window.scrollY>5){r(!1),a(0);return}u=x.touches[0].clientY;const _=u-l;_>10&&!d?(r(!0),n&&(x.preventDefault(),a(Math.max(0,_)))):_<-10&&(d=!0,r(!1),a(0))},[n]),y=k.useCallback(async()=>{if(!(!n&&o===0)){if(r(!1),o>=t&&!d){i(!0);try{await e()}finally{i(!1)}}a(0),d=!1}},[n,o,t,e]),v=k.useCallback(x=>{if(x)return x.addEventListener("touchstart",h,{passive:!0}),x.addEventListener("touchmove",f,{passive:!1}),x.addEventListener("touchend",y,{passive:!0}),()=>{x.removeEventListener("touchstart",h),x.removeEventListener("touchmove",f),x.removeEventListener("touchend",y)}},[h,f,y]);return{isPulling:n,isRefreshing:s,pullDistance:o,bindToElement:v}}function Nw({pullDistance:e,isRefreshing:t,threshold:n=80}){const r=Math.min(e/n,1),s=Math.max(.3,r),i=.5+r*.5;return e===0&&!t?null:c.jsx("div",{className:"flex justify-center items-center py-4 transition-all duration-200",style:{transform:`translateY(${Math.min(e*.5,n*.5)}px)`,opacity:s},children:c.jsxs("div",{className:`flex flex-col items-center gap-2 ${t?"animate-pulse":""}`,style:{transform:`scale(${i})`},children:[c.jsx("div",{className:`${t?"animate-spin":""}`,children:c.jsx(V1,{size:24,className:`${r>=1||t?"text-green-600":"text-gray-400"}`})}),c.jsx("p",{className:"text-xs text-gray-500",children:t?"Refreshing...":r>=1?"Release to refresh":"Pull to refresh"})]})})}function Tw({categories:e,cities:t,selectedCategory:n,selectedCity:r,onCategory:s,onCity:i}){return c.jsxs("div",{className:"flex gap-2 overflow-x-auto pb-2",children:[c.jsx("label",{className:"sr-only",htmlFor:"category",children:"Category"}),c.jsxs("select",{id:"category",className:"px-3 py-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400",value:n,onChange:o=>s(o.target.value),children:[c.jsx("option",{value:"",children:"All Categories"}),e.map(o=>c.jsx("option",{value:o,children:o},o))]}),c.jsx("label",{className:"sr-only",htmlFor:"city",children:"City"}),c.jsxs("select",{id:"city",className:"px-3 py-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400",value:r,onChange:o=>i(o.target.value),children:[c.jsx("option",{value:"",children:"All Cities"}),t.map(o=>c.jsx("option",{value:o,children:o},o))]})]})}function Aw(e={}){const[t,n]=k.useState({latitude:null,longitude:null,error:null,loading:!1});return{...t,getCurrentLocation:()=>{if(!navigator.geolocation){n(s=>({...s,error:"Location services are not supported by this browser",loading:!1}));return}n(s=>({...s,loading:!0,error:null})),navigator.geolocation.getCurrentPosition(s=>{n({latitude:s.coords.latitude,longitude:s.coords.longitude,error:null,loading:!1})},s=>{let i="Unable to get your location";switch(s.code){case s.PERMISSION_DENIED:i="Location access denied. Please enable location services in your browser settings.";break;case s.POSITION_UNAVAILABLE:i="Location information is unavailable. Please try again.";break;case s.TIMEOUT:i="Location request timed out. Please try again.";break}n(o=>({...o,error:i,loading:!1}))},{enableHighAccuracy:!0,timeout:15e3,maximumAge:3e5,...e})},clearLocation:()=>n({latitude:null,longitude:null,error:null,loading:!1})}}function $w(e,t,n,r){const i=Ds(n-e),o=Ds(r-t),a=Math.sin(i/2)*Math.sin(i/2)+Math.cos(Ds(e))*Math.cos(Ds(n))*Math.sin(o/2)*Math.sin(o/2);return 3959*(2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a)))}function Ds(e){return e*(Math.PI/180)}function Ow({onLocationSet:e,onLocationClear:t,hasLocation:n,isLoading:r=!1}){const{latitude:s,longitude:i,error:o,loading:a,getCurrentLocation:l,clearLocation:u}=Aw(),d=()=>{l()},h=()=>{u(),t()};return Mi.useEffect(()=>{s&&i&&e(s,i)},[s,i,e]),c.jsxs("div",{className:"flex items-center gap-2",children:[n?c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsxs("div",{className:"flex items-center gap-1 px-3 py-2 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-lg text-sm font-medium",children:[c.jsx(Ff,{size:16}),"Near Me"]}),c.jsx("button",{onClick:h,className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",title:"Clear location filter",children:c.jsx(cs,{size:16})})]}):c.jsx("button",{onClick:d,disabled:a||r,className:"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors text-sm font-medium",title:"Find businesses near your location",children:a?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Getting Location..."]}):c.jsxs(c.Fragment,{children:[c.jsx(zf,{size:16}),"Near Me"]})}),o&&c.jsx("div",{className:"text-red-600 dark:text-red-400 text-sm",children:o})]})}async function Iw(){if("serviceWorker"in navigator)try{const e=await navigator.serviceWorker.register("/sw.js",{scope:"/"});return console.log("Service Worker registered:",e),e}catch(e){return console.error("Service Worker registration failed:",e),null}return null}async function Rw(){if(!("Notification"in window))throw console.warn("This browser does not support notifications"),new Error("Push notifications are not supported in this browser");if(Notification.permission==="default")try{return await Notification.requestPermission()}catch(e){throw console.error("Failed to request notification permission:",e),new Error("Failed to request notification permission")}return Notification.permission}async function Lw(){try{const e=await Rw();if(e!=="granted")throw console.log("Notification permission not granted:",e),new Error("Notification permission was denied");const t=await Iw();if(!t)throw console.error("Service worker not registered"),new Error("Service worker registration failed");const n=await t.pushManager.getSubscription();if(n)return await Fc(n),n;const r=void 0;let s;r&&r!=="your-vapid-public-key-here"||(console.warn("VAPID public key not configured properly"),s={userVisibleOnly:!0,applicationServerKey:new ArrayBuffer(0)});const i=await t.pushManager.subscribe(s);return await Fc(i),i}catch(e){throw console.error("Failed to subscribe to push notifications:",e),e}}async function Fc(e){localStorage.setItem("dfwbb:push-subscription",JSON.stringify(e));try{const t="https://kecjfdtanszsllmnzhoy.supabase.co";t&&"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtlY2pmZHRhbnN6c2xsbW56aG95Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY3NDAzMTUsImV4cCI6MjA3MjMxNjMxNX0.98it84Jyax4ik1YuqnpMI5nbMffxVLe2EMSaeIPXxHE"&&t!=="your-supabase-url-here"&&await us.from("push_subscriptions").upsert({endpoint:e.endpoint,keys:e.toJSON().keys,created_at:new Date().toISOString()})}catch(t){console.warn("Failed to store subscription in database:",t)}}async function Dw(){const e=await navigator.serviceWorker.getRegistration();if(!e)return!1;try{const t=await e.pushManager.getSubscription();if(t){const n=await t.unsubscribe();if(n){localStorage.removeItem("dfwbb:push-subscription");try{const r="https://kecjfdtanszsllmnzhoy.supabase.co";r&&"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtlY2pmZHRhbnN6c2xsbW56aG95Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY3NDAzMTUsImV4cCI6MjA3MjMxNjMxNX0.98it84Jyax4ik1YuqnpMI5nbMffxVLe2EMSaeIPXxHE"&&r!=="your-supabase-url-here"&&await us.from("push_subscriptions").delete().eq("endpoint",t.endpoint)}catch(r){console.warn("Failed to remove subscription from database:",r)}}return n}}catch(t){console.error("Failed to unsubscribe:",t)}return!1}function Fw(){return"serviceWorker"in navigator&&"PushManager"in window&&"Notification"in window}function Gf(){return Notification.permission==="granted"}function zc(e,t){Gf()&&new Notification(e,{icon:"/icons/icon-192.png",badge:"/icons/icon-72.png",...t})}function Jf(){const e=setInterval(async()=>{try{await Uw()}catch(t){console.error("Error checking for new businesses:",t)}},18e5);localStorage.setItem("dfwbb:monitoring-interval",e.toString())}function zw(){const e=localStorage.getItem("dfwbb:monitoring-interval");e&&(clearInterval(parseInt(e)),localStorage.removeItem("dfwbb:monitoring-interval"))}async function Uw(){const e=localStorage.getItem("dfwbb:last-new-business-check"),t=e?new Date(e):new Date(Date.now()-24*60*60*1e3);try{const{data:n,error:r}=await us.from("businesses").select("name, category, city, created_at").eq("status","approved").gt("created_at",t.toISOString()).order("created_at",{ascending:!1});!r&&n&&n.length>0&&(n.length===1?zc("New Business Added!",{body:`${n[0].name} (${n[0].category}) in ${n[0].city}`,tag:"new-business",requireInteraction:!1}):zc(`${n.length} New Businesses Added!`,{body:"Check out the latest Black-owned businesses in DFW",tag:"new-businesses",requireInteraction:!1})),localStorage.setItem("dfwbb:last-new-business-check",new Date().toISOString())}catch(n){console.warn("Failed to check for new businesses:",n)}}function Bw(){const[e,t]=k.useState(!1),[n,r]=k.useState(!1),[s,i]=k.useState(!1),[o,a]=k.useState(!1);k.useEffect(()=>{const d=Fw(),h=Gf();t(d),r(h);const f=localStorage.getItem("dfwbb:push-subscription");i(!!f&&h)},[]);const l=async()=>{a(!0);try{if(!("Notification"in window)){alert("Push notifications are not supported in this browser.");return}if(await Notification.requestPermission()!=="granted"){alert("Notification permission was denied. Please enable notifications in your browser settings and try again.");return}await Lw()?(i(!0),r(!0),Jf(),alert("Push notifications enabled! You'll be notified of new businesses.")):alert("Failed to enable push notifications. Please make sure notifications are allowed in your browser settings.")}catch(d){console.error("Subscription failed:",d),alert("Failed to enable push notifications. Please check your browser settings and try again.")}finally{a(!1)}},u=async()=>{a(!0);try{await Dw()?(i(!1),zw(),alert("Push notifications disabled.")):alert("Failed to disable notifications.")}catch(d){console.error("Unsubscription failed:",d),alert("Failed to disable notifications. Please try again.")}finally{a(!1)}};return e?c.jsx("div",{className:"flex items-center gap-2",children:s?c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsxs("div",{className:"flex items-center gap-1 px-3 py-2 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-lg text-sm",children:[c.jsx(Ac,{size:16}),c.jsx("span",{children:"Notifications On"})]}),c.jsx("button",{onClick:u,disabled:o,className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",title:"Disable notifications",children:c.jsx(Q1,{size:16})})]}):c.jsx("button",{onClick:l,disabled:o,className:"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white text-sm rounded-lg transition-colors",title:"Get notified of new businesses",children:o?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Enabling..."]}):c.jsxs(c.Fragment,{children:[c.jsx(Ac,{size:16}),"Enable Notifications"]})})}):c.jsxs("div",{className:"flex items-center gap-2 text-gray-500 text-sm",children:[c.jsx(n1,{size:16}),c.jsx("span",{children:"Push notifications not supported in this browser"})]})}const Qf="dfwbb:recently-viewed";function qa(){try{return JSON.parse(localStorage.getItem(Qf)||"[]")}catch{return[]}}function Ww(e){const t=qa(),n=t.indexOf(e);n>-1&&t.splice(n,1),t.unshift(e);const r=t.slice(0,10);localStorage.setItem(Qf,JSON.stringify(r))}function Hw(){const[e,t]=k.useState(Ys),[n,r]=k.useState(""),[s,i]=k.useState(""),[o,a]=k.useState(""),[l,u]=k.useState(null),[d,h]=k.useState(!1),[f,y]=k.useState("name"),[v,x]=k.useState([]),[_,m]=k.useState(!1),[p,g]=k.useState(!0),[w,S]=k.useState(null),b=k.useRef(null),P=Ew(o,200),M=async()=>{g(!0);let j=[...Ys];try{const{data:E,error:F}=await us.from("businesses").select("*").eq("status","approved");!F&&E&&(j=Vw([...Ys],E))}catch(E){console.warn("Supabase connection failed during refresh:",E)}try{_w(j)}catch(E){console.warn("Business change detection failed:",E)}t(j),await new Promise(E=>setTimeout(E,500)),g(!1)},{isRefreshing:O,pullDistance:A,bindToElement:q}=Mw({onRefresh:M,threshold:120});k.useEffect(()=>{const j=b.current;if(j)return q(j)},[q]),k.useEffect(()=>{M(),x(qa()),Jf()},[]);const Ge=k.useMemo(()=>Array.from(new Set(e.map(j=>j.category))).sort(),[e]),Je=k.useMemo(()=>Array.from(new Set(e.map(j=>j.city))).sort(),[e]),gt=k.useMemo(()=>{let j=e.filter(E=>{const F=n?E.category===n:!0,X=s?E.city===s:!0,Wt=P.toLowerCase(),nt=!Wt||[E.name,E.category,E.city,E.description??""].some(nr=>nr.toLowerCase().includes(Wt));return F&&X&&nt});return w&&(j=j.map(E=>{let F;return E.coordinates?F=$w(w.latitude,w.longitude,E.coordinates.latitude,E.coordinates.longitude):E.city&&(F={Dallas:15,"Fort Worth":25,Arlington:20,Plano:30,Irving:18,Garland:22,"Grand Prairie":24,Mesquite:25,Carrollton:28,Richardson:25,Denton:35,McKinney:40,Frisco:35,Lewisville:30}[E.city]||30),{...E,distance:F}})),j},[e,n,s,P,w]),fn=k.useMemo(()=>[...gt].sort((j,E)=>{switch(f){case"distance":return j.distance&&E.distance?j.distance-E.distance:0;case"rating":return(E.rating||0)-(j.rating||0);case"reviewCount":return(E.reviewCount||0)-(j.reviewCount||0);case"category":return j.category.localeCompare(E.category);case"name":default:return j.name.localeCompare(E.name)}}),[gt,f]),pn=k.useMemo(()=>v.map(j=>e.find(E=>E.name===j)).filter(Boolean),[v,e]),Bt=j=>{Ww(j.name),x(qa()),u(j),h(!0)},N=()=>{h(!1),u(null)};return c.jsxs("div",{ref:b,className:"p-4 max-w-4xl mx-auto",children:[c.jsx(Nw,{pullDistance:A,isRefreshing:O}),c.jsxs("div",{className:"mb-6",children:[c.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"DFW Black Business Hub"}),c.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Discover and support Black-owned businesses in the Dallas-Fort Worth area"})]}),c.jsx("div",{className:"mb-4",children:c.jsx(Cw,{value:o,onChange:a,businesses:e,placeholder:"Search businesses, categories, or cities...",onBusinessSelect:Bt})}),c.jsxs("div",{className:"mb-4 space-y-3",children:[c.jsx(Tw,{categories:Ge,cities:Je,selectedCategory:n,selectedCity:s,onCategory:r,onCity:i}),c.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-3",children:[c.jsx(Ow,{onLocationSet:(j,E)=>S({latitude:j,longitude:E}),onLocationClear:()=>S(null),hasLocation:!!w,isLoading:p}),c.jsx(Bw,{})]}),c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Sort by:"}),c.jsxs("select",{className:"px-3 py-1 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded text-sm focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400",value:f,onChange:j=>y(j.target.value),children:[c.jsx("option",{value:"name",children:"Name"}),c.jsx("option",{value:"rating",children:"Rating"}),c.jsx("option",{value:"reviewCount",children:"Most Reviews"}),c.jsx("option",{value:"category",children:"Category"}),w&&c.jsx("option",{value:"distance",children:"Distance"})]})]}),v.length>0&&c.jsxs("button",{onClick:()=>m(!_),className:"text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1",children:[c.jsx("span",{children:"📖"}),"Recently Viewed (",v.length,")"]})]})]}),_&&pn.length>0&&c.jsxs("div",{className:"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border dark:border-gray-700",children:[c.jsxs("h2",{className:"font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2",children:[c.jsx("span",{children:"📖"}),"Recently Viewed"]}),c.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:pn.slice(0,4).map((j,E)=>c.jsx(Va,{b:j,onClick:()=>Bt(j)},j.name+E))})]}),p?c.jsxs("div",{children:[c.jsxs("div",{className:"mb-4 flex items-center gap-2 text-gray-600",children:[c.jsx("div",{className:"animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full"}),"Loading businesses..."]}),c.jsx(jw,{count:6})]}):c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 mt-3",children:fn.map((j,E)=>c.jsx(Va,{b:j,onClick:()=>Bt(j)},j.name+E))}),fn.length===0&&c.jsxs("div",{className:"mt-8 text-center py-8",children:[c.jsx("div",{className:"text-gray-400 text-6xl mb-4",children:"🔍"}),c.jsx("p",{className:"text-gray-500 mb-4 text-lg",children:"No matches found."}),c.jsx("button",{onClick:()=>{r(""),i(""),a("")},className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Clear all filters"})]}),fn.length>0&&c.jsxs("div",{className:"mt-6 text-center text-sm text-gray-600 border-t pt-4",children:["Showing ",fn.length," of ",e.length," businesses"]})]}),c.jsx(Kf,{business:l,isOpen:d,onClose:N})]})}function Vw(e,t){const n=new Set(e.map(r=>r.name));return[...e,...t.filter(r=>!n.has(r.name))]}function qw(){const[e,t]=k.useState("idle");async function n(r){r.preventDefault();const s=new FormData(r.currentTarget),i=(s.get("name")||"").toString().trim(),o=(s.get("category")||"").toString().trim(),a=(s.get("city")||"").toString().trim(),l=(s.get("website")||"").toString().trim(),u=(s.get("description")||"").toString().trim();if(!i||!o||!a){alert("Please fill required fields.");return}t("loading");const{error:d}=await us.from("businesses").insert([{name:i,category:o,city:a,website:l,description:u,status:"pending"}]);t(d?"error":"success"),d||r.currentTarget.reset()}return c.jsxs("div",{className:"p-4 max-w-xl mx-auto",children:[c.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Recommend a Business"}),c.jsxs("form",{onSubmit:n,className:"space-y-3",children:[c.jsxs("label",{className:"block",children:[c.jsx("span",{className:"text-sm",children:"Business name*"}),c.jsx("input",{name:"name",className:"w-full px-3 py-2 rounded border",required:!0})]}),c.jsxs("label",{className:"block",children:[c.jsx("span",{className:"text-sm",children:"Category*"}),c.jsx("input",{name:"category",className:"w-full px-3 py-2 rounded border",required:!0})]}),c.jsxs("label",{className:"block",children:[c.jsx("span",{className:"text-sm",children:"City*"}),c.jsx("input",{name:"city",className:"w-full px-3 py-2 rounded border",required:!0})]}),c.jsxs("label",{className:"block",children:[c.jsx("span",{className:"text-sm",children:"Website (optional)"}),c.jsx("input",{name:"website",className:"w-full px-3 py-2 rounded border"})]}),c.jsxs("label",{className:"block",children:[c.jsx("span",{className:"text-sm",children:"Description (optional)"}),c.jsx("textarea",{name:"description",className:"w-full px-3 py-2 rounded border",rows:3})]}),c.jsx("button",{disabled:e==="loading",className:"px-4 py-2 bg-black text-white rounded",children:e==="loading"?"Submitting...":"Submit"})]}),e==="success"&&c.jsx("p",{className:"mt-3 text-green-600",children:"Thanks! Submission pending approval."}),e==="error"&&c.jsx("p",{className:"mt-3 text-red-600",children:"Submission failed. Please try again."})]})}function Kw(){const{favorites:e,removeFromFavorites:t}=Gl(),[n,r]=k.useState(null),s=e.map(o=>Ys.find(l=>l.name===o.businessName)).filter(o=>o!==void 0).sort((o,a)=>{const l=e.find(d=>d.businessName===o.name),u=e.find(d=>d.businessName===a.name);return!l||!u?0:new Date(u.addedAt).getTime()-new Date(l.addedAt).getTime()}),i=()=>{window.confirm("Are you sure you want to remove all favorites?")&&e.forEach(o=>t(o.businessName))};return s.length===0?c.jsx("div",{className:"max-w-4xl mx-auto p-6",children:c.jsxs("div",{className:"text-center py-12",children:[c.jsx(Ce,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),c.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"No Favorites Yet"}),c.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:"Start exploring businesses and add them to your favorites by clicking the heart icon."}),c.jsx("a",{href:"/",className:"inline-block bg-blue-600 dark:bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors",children:"Browse Directory"})]})}):c.jsxs("div",{className:"max-w-4xl mx-auto p-6",children:[c.jsxs("div",{className:"flex justify-between items-center mb-6",children:[c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"My Favorites"}),c.jsxs("p",{className:"text-gray-600 dark:text-gray-300 mt-1",children:[s.length," saved business",s.length!==1?"es":""]})]}),s.length>0&&c.jsxs("button",{onClick:i,className:"flex items-center gap-2 px-4 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors",children:[c.jsx(uw,{size:16}),"Clear All"]})]}),c.jsx("div",{className:"grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3",children:s.map(o=>{const a=e.find(l=>l.businessName===o.name);return a?c.jsxs("div",{className:"relative",children:[c.jsx(Va,{b:o,onClick:()=>r(o)}),c.jsxs("div",{className:"absolute top-2 right-2 text-xs text-gray-500 dark:text-gray-400 bg-white/90 dark:bg-gray-800/90 px-2 py-1 rounded backdrop-blur-sm",children:["Added ",new Date(a.addedAt).toLocaleDateString()]})]},o.name):null})}),c.jsx(Kf,{business:n,isOpen:!!n,onClose:()=>r(null)})]})}function Gw(){const[e,t]=k.useState(null),[n,r]=k.useState(!1),[s,i]=k.useState(!1),[o,a]=k.useState(!1),[l,u]=k.useState(!1);k.useEffect(()=>{const y=navigator.userAgent,v=/iPad|iPhone|iPod/.test(y)&&!window.MSStream,x=/Android/.test(y);i(v),a(x);const _=window.matchMedia("(display-mode: standalone)").matches,m=navigator.standalone===!0;(_||m)&&u(!0);const p=g=>{console.log("beforeinstallprompt event fired"),g.preventDefault(),t(g)};return window.addEventListener("beforeinstallprompt",p),window.addEventListener("appinstalled",()=>{console.log("App was installed"),u(!0),t(null)}),console.log("PWA Install Component:",{isIOSDevice:v,isAndroidDevice:x,isStandalone:_,isIOSStandalone:m,userAgent:navigator.userAgent}),()=>{window.removeEventListener("beforeinstallprompt",p)}},[]);const d=async()=>{if(console.log("Install button clicked:",{isIOS:s,deferredPrompt:!!e}),s){r(!0);return}if(e)try{console.log("Triggering install prompt"),await e.prompt();const y=await e.userChoice;console.log("User choice:",y.outcome),y.outcome==="accepted"&&u(!0),t(null)}catch(y){console.error("Error during installation:",y)}else console.log("No deferred prompt available. This could mean:"),console.log("1. PWA criteria not met"),console.log("2. App already installed"),console.log("3. Browser doesn't support PWA installation"),console.log("4. Running in development mode")};if(l)return null;const h=()=>"Install App";return c.jsxs(c.Fragment,{children:[c.jsxs("button",{onClick:d,className:"flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors",title:"Install App",children:[c.jsx(m1,{size:16}),c.jsx("span",{className:"hidden sm:inline",children:h()}),!1]}),n&&s&&c.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50",children:c.jsxs("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[c.jsxs("div",{className:"flex items-center justify-between mb-4",children:[c.jsx("h3",{className:"text-lg font-semibold",children:"Install App"}),c.jsx("button",{onClick:()=>r(!1),className:"text-gray-400 hover:text-gray-600",children:c.jsx(cs,{size:24})})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsx("p",{className:"text-gray-600",children:"To install this app on your iPhone or iPad:"}),c.jsxs("ol",{className:"space-y-3 text-sm",children:[c.jsxs("li",{className:"flex items-start gap-3",children:[c.jsx("span",{className:"bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-semibold",children:"1"}),c.jsxs("div",{children:["Tap the ",c.jsx(Z1,{size:16,className:"inline mx-1"}),c.jsx("strong",{children:"Share"})," button at the bottom of Safari"]})]}),c.jsxs("li",{className:"flex items-start gap-3",children:[c.jsx("span",{className:"bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-semibold",children:"2"}),c.jsxs("div",{children:["Scroll down and tap",c.jsx(W1,{size:16,className:"inline mx-1"}),c.jsx("strong",{children:'"Add to Home Screen"'})]})]}),c.jsxs("li",{className:"flex items-start gap-3",children:[c.jsx("span",{className:"bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-xs font-semibold",children:"3"}),c.jsxs("div",{children:["Tap ",c.jsx("strong",{children:'"Add"'})," to confirm"]})]})]}),c.jsx("p",{className:"text-xs text-gray-500 mt-4",children:"The app will appear on your home screen and work just like a native app!"})]}),c.jsx("button",{onClick:()=>r(!1),className:"w-full mt-6 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-colors",children:"Got it!"})]})})]})}const Xf=k.createContext(void 0);function Jw({children:e}){const[t,n]=k.useState(()=>typeof window>"u"?"system":localStorage.getItem("theme")||"system"),[r,s]=k.useState("light");return k.useEffect(()=>{const i=window.document.documentElement,o=()=>{let a;t==="system"?a=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":a=t,s(a),i.classList.remove("light","dark"),i.classList.add(a)};if(o(),localStorage.setItem("theme",t),t==="system"){const a=window.matchMedia("(prefers-color-scheme: dark)"),l=()=>o();return a.addListener(l),()=>a.removeListener(l)}},[t]),c.jsx(Xf.Provider,{value:{theme:t,resolvedTheme:r,setTheme:n},children:e})}function Qw(){const e=k.useContext(Xf);if(e===void 0)throw new Error("useTheme must be used within a ThemeProvider");return e}function Xw(){const{theme:e,setTheme:t}=Qw(),n=[{value:"light",icon:iw,label:"Light"},{value:"dark",icon:R1,label:"Dark"},{value:"system",icon:O1,label:"System"}];return c.jsx("div",{className:"flex items-center gap-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:n.map(({value:r,icon:s,label:i})=>c.jsx("button",{onClick:()=>t(r),className:`
            p-2 rounded-md text-sm font-medium transition-all duration-200
            ${e===r?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-gray-700/50"}
          `,title:i,"aria-label":`Switch to ${i.toLowerCase()} theme`,children:c.jsx(s,{size:16})},r))})}const Yw=()=>{const[e,t]=k.useState([]),n=s=>{const i=Date.now().toString(),o={...s,id:i};t(l=>[...l,o]);const a=s.duration??5e3;a>0&&setTimeout(()=>{r(i)},a)},r=s=>{t(i=>i.filter(o=>o.id!==s))};return{notifications:e,addNotification:n,removeNotification:r}};function Zw({notifications:e,onRemove:t}){return e.length===0?null:c.jsx("div",{className:"fixed bottom-4 right-4 z-50 space-y-2 max-w-sm",children:e.map(n=>c.jsx(ex,{notification:n,onRemove:()=>t(n.id)},n.id))})}function ex({notification:e,onRemove:t}){const[n,r]=k.useState(!1);k.useEffect(()=>{setTimeout(()=>r(!0),10)},[]);const s=()=>{r(!1),setTimeout(t,200)},i=()=>{switch(e.type){case"success":return c.jsx(u1,{size:20});case"error":return c.jsx(ji,{size:20});case"warning":return c.jsx(ji,{size:20});case"info":default:return c.jsx(j1,{size:20})}},o=()=>{switch(e.type){case"success":return"bg-green-50 border-green-200 text-green-800";case"error":return"bg-red-50 border-red-200 text-red-800";case"warning":return"bg-yellow-50 border-yellow-200 text-yellow-800";case"info":default:return"bg-blue-50 border-blue-200 text-blue-800"}};return c.jsx("div",{className:`
        transform transition-all duration-200 ease-out
        ${n?"translate-x-0 opacity-100":"translate-x-full opacity-0"}
        ${o()}
        border rounded-lg p-4 shadow-lg backdrop-blur-sm
      `,children:c.jsxs("div",{className:"flex items-start gap-3",children:[c.jsx("div",{className:"flex-shrink-0",children:i()}),c.jsxs("div",{className:"flex-1 min-w-0",children:[c.jsx("h4",{className:"font-medium text-sm",children:e.title}),e.message&&c.jsx("p",{className:"text-sm opacity-90 mt-1",children:e.message})]}),c.jsx("button",{onClick:s,className:"flex-shrink-0 p-1 rounded-full hover:bg-black/5 transition-colors",children:c.jsx(cs,{size:16})})]})})}function tx(){const e=is(),t=[{path:"/",icon:Wa,label:"Directory",isActive:e.pathname==="/"},{path:"/favorites",icon:Ce,label:"Favorites",isActive:e.pathname==="/favorites"},{path:"/submit",icon:d1,label:"Submit",isActive:e.pathname==="/submit"}];return c.jsx("nav",{className:"sm:hidden fixed bottom-0 left-0 right-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur border-t dark:border-gray-700 z-50 safe-area-pb",children:c.jsx("div",{className:"flex justify-around items-center px-2 py-2 max-w-md mx-auto",children:t.map(({path:n,icon:r,label:s,isActive:i})=>c.jsxs(vr,{to:n,className:`flex flex-col items-center gap-1 px-3 py-2 rounded-lg transition-colors min-w-0 flex-1 ${i?"text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"}`,children:[c.jsx(r,{size:20,className:"flex-shrink-0"}),c.jsx("span",{className:"text-xs font-medium truncate w-full text-center",children:s})]},n))})})}function nx(){const{notifications:e,addNotification:t,removeNotification:n}=Yw();return k.useEffect(()=>{Jv((r,s)=>{t(r==="add"?{type:"success",title:"Added to Favorites",message:`${s} has been added to your favorites!`,duration:3e3}:{type:"info",title:"Removed from Favorites",message:`${s} has been removed from your favorites.`,duration:3e3})})},[t]),c.jsx(Jw,{children:c.jsxs(Pm,{children:[c.jsxs("div",{className:"min-h-dvh bg-neutral-50 dark:bg-gray-900 text-neutral-900 dark:text-white transition-colors pb-16 sm:pb-0",children:[c.jsx("header",{className:"sticky top-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur border-b dark:border-gray-700 z-40",children:c.jsxs("nav",{className:"max-w-4xl mx-auto flex items-center justify-between p-3",children:[c.jsx(vr,{to:"/",className:"font-bold",children:"DFW Black Business Hub"}),c.jsxs("div",{className:"flex items-center gap-4",children:[c.jsx(vr,{to:"/",className:"hover:text-blue-600 transition-colors hidden sm:block",children:"Directory"}),c.jsxs(vr,{to:"/favorites",className:"hover:text-blue-600 transition-colors hidden sm:flex items-center gap-1",children:[c.jsx(Ce,{size:16}),"Favorites"]}),c.jsx(vr,{to:"/submit",className:"hover:text-blue-600 transition-colors hidden sm:block",children:"Recommend"}),c.jsx(Xw,{}),c.jsx(Gw,{})]})]})}),c.jsx("main",{className:"max-w-5xl mx-auto",children:c.jsxs(wm,{children:[c.jsx(Xs,{path:"/",element:c.jsx(Hw,{})}),c.jsx(Xs,{path:"/favorites",element:c.jsx(Kw,{})}),c.jsx(Xs,{path:"/submit",element:c.jsx(qw,{})})]})}),c.jsx(tx,{})]}),c.jsx(Zw,{notifications:e,onRemove:n})]})})}"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").catch(()=>{})});window.addEventListener("beforeinstallprompt",e=>{e.preventDefault()});Ro.createRoot(document.getElementById("root")).render(c.jsx(Mi.StrictMode,{children:c.jsx(nx,{})}));
