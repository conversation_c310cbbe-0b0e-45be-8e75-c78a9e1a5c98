@tailwind base;
@tailwind components;
@tailwind utilities;

:root { 
  color-scheme: light dark; 
}

/* Dark mode body styles */
body {
  background-color: theme('colors.gray.50');
}

body.dark {
  background-color: theme('colors.gray.900');
  color: theme('colors.white');
}

/* Custom utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Ensure bottom navigation stays fixed and doesn't interfere with scrolling */
.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Additional mobile optimization */
@media (max-width: 639px) {
  body {
    overscroll-behavior-y: contain; /* Prevent bounce scroll but allow pull-to-refresh when at top */
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  }
  
  /* Prevent accidental pull-to-refresh on elements that shouldn't trigger it */
  .no-pull-refresh {
    overscroll-behavior-y: none;
  }
  
  /* Main app container should handle scroll properly */
  #root {
    overscroll-behavior-y: contain;
  }
}
