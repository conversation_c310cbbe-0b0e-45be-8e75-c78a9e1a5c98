import { BrowserRouter, Routes, Route, Link } from 'react-router-dom';
import Directory from './pages/Directory';
import Submit from './pages/Submit';
import Favorites from './pages/Favorites';
import InstallPWA from './components/InstallPWA';
import { ThemeProvider } from './hooks/useTheme';
import { ThemeToggle } from './components/ThemeToggle';
import { useNotifications, NotificationContainer } from './hooks/useNotifications';
import { setFavoriteNotificationCallback } from './hooks/useFavorites';
import { BottomNavigation } from './components/BottomNavigation';
import { Heart } from 'lucide-react';
import { useEffect } from 'react';

export default function App() {
  const { notifications, addNotification, removeNotification } = useNotifications();

  useEffect(() => {
    // Set up notification callback for favorites
    setFavoriteNotificationCallback((type, businessName) => {
      if (type === 'add') {
        addNotification({
          type: 'success',
          title: 'Added to Favorite<PERSON>',
          message: `${businessName} has been added to your favorites!`,
          duration: 3000,
        });
      } else {
        addNotification({
          type: 'info',
          title: 'Removed from Favorite<PERSON>',
          message: `${businessName} has been removed from your favorites.`,
          duration: 3000,
        });
      }
    });
  }, [addNotification]);

  return (
    <ThemeProvider>
      <BrowserRouter>
        <div className="min-h-dvh bg-neutral-50 dark:bg-gray-900 text-neutral-900 dark:text-white transition-colors pb-16 sm:pb-0">
          <header className="sticky top-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur border-b dark:border-gray-700 z-40">
            <nav className="max-w-4xl mx-auto flex items-center justify-between p-3">
              <Link to="/" className="font-bold">DFW Black Business Hub</Link>
              <div className="flex items-center gap-4">
                <Link to="/" className="hover:text-blue-600 transition-colors hidden sm:block">Directory</Link>
                <Link to="/favorites" className="hover:text-blue-600 transition-colors hidden sm:flex items-center gap-1">
                  <Heart size={16} />
                  Favorites
                </Link>
                <Link to="/submit" className="hover:text-blue-600 transition-colors hidden sm:block">Recommend</Link>
                <ThemeToggle />
                <InstallPWA />
              </div>
            </nav>
          </header>
          <main className="max-w-5xl mx-auto">
            <Routes>
              <Route path="/" element={<Directory />} />
              <Route path="/favorites" element={<Favorites />} />
              <Route path="/submit" element={<Submit />} />
            </Routes>
          </main>
          
          {/* Bottom Navigation for Mobile */}
          <BottomNavigation />
        </div>
        <NotificationContainer 
          notifications={notifications} 
          onRemove={removeNotification} 
        />
      </BrowserRouter>
    </ThemeProvider>
  );
}

